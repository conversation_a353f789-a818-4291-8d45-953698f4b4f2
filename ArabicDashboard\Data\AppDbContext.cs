using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.IO;
using ArabicDashboard.Models;

namespace ArabicDashboard.Data
{
    public class AppDbContext : DbContext
    {
        public DbSet<SimpleTransactionEntity> SimpleTransactions { get; set; }
        public DbSet<AgentTransactionEntity> AgentTransactions { get; set; }
        public DbSet<Worker> Workers { get; set; }
        public DbSet<Residence> Residences { get; set; }
    
        public DbSet<SharedAccount> SharedAccounts { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<MoneyTransfer> MoneyTransfers { get; set; }
        public DbSet<Reminder> Reminders { get; set; }
        public DbSet<InquiryCalculation> InquiryCalculations { get; set; }
        public DbSet<Archive> Archives { get; set; }
        public DbSet<ResidenceInfo> ResidenceInfos { get; set; }
        public DbSet<WorkPermitInfo> WorkPermitInfos { get; set; }
        public DbSet<TaskItem> Tasks { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // إنشاء قاعدة البيانات في مجلد التطبيق
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ArabicDashboard.db");
            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // تكوين جدول المعاملات العادية
            modelBuilder.Entity<SimpleTransactionEntity>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ClientName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ServiceType).IsRequired().HasMaxLength(300);
                entity.Property(e => e.ServiceFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.GovernmentFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.LaborOfficeFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.NetProfit).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Status).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.EmploymentAmount).HasColumnType("decimal(18,2)");
            });

            // تكوين جدول معاملات المنفذين
            modelBuilder.Entity<AgentTransactionEntity>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AgentName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ServiceType).HasMaxLength(300);
                entity.Property(e => e.ServiceFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.NetProfit).HasColumnType("decimal(18,2)");
            });

            // تكوين جدول العمال
            modelBuilder.Entity<Worker>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.WorkerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ResidenceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SponsorName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SponsorIdNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SponsorIdImagePath).HasMaxLength(500);
                entity.Property(e => e.CommercialRegistrationPath).HasMaxLength(500);
                entity.Property(e => e.QawaUsername).HasMaxLength(100);
                entity.Property(e => e.QawaPassword).HasMaxLength(100);
                entity.Property(e => e.AbsherUsername).HasMaxLength(100);
                entity.Property(e => e.AbsherPassword).HasMaxLength(100);
                entity.Property(e => e.ChamberUsername).HasMaxLength(100);
                entity.Property(e => e.ChamberPassword).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(1000);
            });

            // تكوين جدول الإقامات
            modelBuilder.Entity<Residence>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.WorkerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ResidenceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.RenewalDate).IsRequired();
                entity.Property(e => e.ExpiryDate).IsRequired();
                entity.Property(e => e.MedicalInsuranceExpiryDate).IsRequired();
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.CreatedDate).IsRequired();
            });

            // تكوين جدول الحسابات المشتركة
            modelBuilder.Entity<SharedAccount>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AccountName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.WorkerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ServiceType).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(500);

                // تكوين الحقول المالية
                entity.Property(e => e.SponsorAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.OfficeAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PassportFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.WorkPermitFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RecruitmentAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ServiceFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MedicalInsurance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TransferFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalGrossAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalExpenses).HasColumnType("decimal(18,2)");
                entity.Property(e => e.NetProfit).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ProfitShare).HasColumnType("decimal(18,2)");
            });

            // تكوين جدول الفواتير والسندات
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ClientName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(100);
                entity.Property(e => e.TransactionType).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.ClientIdNumber).HasMaxLength(50);
                entity.Property(e => e.ClientAddress).HasMaxLength(300);
                entity.Property(e => e.InvoiceType).HasMaxLength(20);
                entity.Property(e => e.QRCode).HasMaxLength(100);
                entity.Property(e => e.TaxNumber).HasMaxLength(50);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);

                // فهرس فريد لرقم الفاتورة
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
            });

            // تكوين جدول التذكيرات
            modelBuilder.Entity<Reminder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TransactionName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ClientName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(150);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Notes).HasMaxLength(300);
                entity.Property(e => e.PhoneNumber).HasMaxLength(20);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.ReminderDateTime).IsRequired();
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.UpdatedDate).IsRequired();
            });

            // تكوين جدول الاستعلامات والحسابات
            modelBuilder.Entity<InquiryCalculation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.WorkerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ResidenceNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.ExpiryDate).IsRequired();
                entity.Property(e => e.WorkerType).IsRequired();
                entity.Property(e => e.ServiceType).IsRequired();
                entity.Property(e => e.PassportFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.WorkPermitFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MedicalInsurance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.SaudiEmployment).HasColumnType("decimal(18,2)");
                entity.Property(e => e.LaborOfficeFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ServiceFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TransferFees).HasColumnType("decimal(18,2)");
                entity.Property(e => e.RequiresManualRenewal).IsRequired();
                entity.Property(e => e.WorkPermitIssueDate);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.CreatedDate).IsRequired();

                // فهارس للبحث السريع
                entity.HasIndex(e => e.WorkerName);
                entity.HasIndex(e => e.ResidenceNumber);
                entity.HasIndex(e => e.WorkerType);
                entity.HasIndex(e => e.ServiceType);
                entity.HasIndex(e => e.ExpiryDate);
                entity.HasIndex(e => e.CreatedDate);
            });

            // تكوين جدول معلومات الإقامة
            modelBuilder.Entity<ResidenceInfo>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ResidenceNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.HolderName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Nationality).HasMaxLength(50);
                entity.Property(e => e.ResidenceType).HasMaxLength(50);
                entity.Property(e => e.SponsorName).HasMaxLength(100);
                entity.Property(e => e.SponsorId).HasMaxLength(20);
                entity.Property(e => e.DataSource).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.IssueDate).IsRequired();
                entity.Property(e => e.ExpiryDate).IsRequired();
                entity.Property(e => e.LastUpdated).IsRequired();

                // فهارس للبحث السريع
                entity.HasIndex(e => e.ResidenceNumber).IsUnique();
                entity.HasIndex(e => e.HolderName);
                entity.HasIndex(e => e.ExpiryDate);
                entity.HasIndex(e => e.Status);
            });

            // تكوين جدول معلومات رخص العمل
            modelBuilder.Entity<WorkPermitInfo>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ResidenceNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.WorkPermitNumber).HasMaxLength(20);
                entity.Property(e => e.EmployerName).HasMaxLength(100);
                entity.Property(e => e.JobType).HasMaxLength(100);
                entity.Property(e => e.LastUpdated).IsRequired();

                // فهارس للبحث السريع
                entity.HasIndex(e => e.ResidenceNumber);
                entity.HasIndex(e => e.WorkPermitNumber);
                entity.HasIndex(e => e.Status);
            });

            // تكوين جدول الأرشيف
            modelBuilder.Entity<Archive>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ArchiveName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ArchiveDate).IsRequired();
                entity.Property(e => e.ArchiveType).IsRequired().HasMaxLength(20);
                entity.Property(e => e.DataJson).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.UpdatedDate);
                entity.Property(e => e.IsActive).IsRequired();

                // فهارس للبحث السريع
                entity.HasIndex(e => e.ArchiveDate);
                entity.HasIndex(e => e.ArchiveType);
                entity.HasIndex(e => e.IsActive);
            });

            // تكوين جدول التحويلات المالية
            modelBuilder.Entity<MoneyTransfer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TransferNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SenderName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SenderPhone).IsRequired().HasMaxLength(20);
                entity.Property(e => e.SenderBank).HasMaxLength(100);
                entity.Property(e => e.ReceiptPath).HasMaxLength(500);
                entity.Property(e => e.ReceiverName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.ReceiverPhone).IsRequired().HasMaxLength(20);
                entity.Property(e => e.ReceiverCountry).HasMaxLength(100);
                entity.Property(e => e.ReceiverCity).HasMaxLength(100);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ServiceFeeRate).HasColumnType("decimal(18,4)");
                entity.Property(e => e.TaxRate).HasColumnType("decimal(18,4)");
                entity.Property(e => e.ServiceFeeAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TaxAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.TransferDate).IsRequired();
                entity.Property(e => e.CreatedDate).IsRequired();

                // فهارس للبحث السريع
                entity.HasIndex(e => e.TransferNumber).IsUnique();
                entity.HasIndex(e => e.SenderName);
                entity.HasIndex(e => e.SenderPhone);
                entity.HasIndex(e => e.ReceiverName);
                entity.HasIndex(e => e.ReceiverPhone);
                entity.HasIndex(e => e.TransferDate);
                entity.HasIndex(e => e.Status);
            });

            // تكوين جدول المهام
            modelBuilder.Entity<TaskItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.AssignedTo).HasMaxLength(100);
                entity.Property(e => e.CreatedBy).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.CreatedDate).IsRequired();

                // فهارس للبحث السريع
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.DueDate);
                entity.HasIndex(e => e.AssignedTo);
            });

            base.OnModelCreating(modelBuilder);
        }
    }

    // نموذج قاعدة البيانات للمعاملات العادية
    public class SimpleTransactionEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string ClientName { get; set; } = "";

        [Required]
        [MaxLength(300)]
        public string ServiceType { get; set; } = "";

        public decimal ServiceFees { get; set; }
        public decimal GovernmentFees { get; set; }
        public decimal LaborOfficeFees { get; set; }
        public decimal NetProfit { get; set; }
        public decimal TotalAmount { get; set; }

        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = "قيد التنفيذ";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [MaxLength(1000)]
        public string Notes { get; set; } = "";

        // حقول التوظيف للعمالة المنزلية
        public bool IsEmployed { get; set; } = false;
        public decimal EmploymentAmount { get; set; } = 0;
    }

    // نموذج قاعدة البيانات لمعاملات المنفذين
    public class AgentTransactionEntity
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string AgentName { get; set; } = "";
        
        [MaxLength(300)]
        public string ServiceType { get; set; } = "";
        
        public decimal ServiceFees { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal NetProfit { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = "قيد التنفيذ";
        
        [Required]
        [MaxLength(50)]
        public string TransferStatus { get; set; } = "لم يتم التحويل";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = "";
    }


}
