<Window x:Class="ArabicDashboard.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - مكتب الخدمات العامة"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- تأثيرات الظل -->
        <DropShadowEffect x:Key="CardShadow" Color="Black" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="15"/>
        
        <!-- تدرجات الألوان -->
        <LinearGradientBrush x:Key="MainGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="CardGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#ffffff" Offset="0"/>
            <GradientStop Color="#f8fafc" Offset="1"/>
        </LinearGradientBrush>
        
        <!-- أنماط الأزرار -->
        <Style x:Key="LoginButton" TargetType="Button">
            <Setter Property="Background" Value="#667eea"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                Effect="{StaticResource CardShadow}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5a67d8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- أنماط حقول الإدخال -->
        <Style x:Key="LoginTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#667eea"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- أنماط حقول كلمة المرور -->
        <Style x:Key="LoginPasswordBox" TargetType="PasswordBox">
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#e2e8f0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#667eea"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- الخلفية المتدرجة -->
        <Rectangle Fill="{StaticResource MainGradient}"/>
        
        <!-- أشكال هندسية للزينة -->
        <Canvas>
            <Ellipse Width="200" Height="200" Fill="#ffffff" Opacity="0.1" Canvas.Left="-50" Canvas.Top="-50"/>
            <Ellipse Width="150" Height="150" Fill="#ffffff" Opacity="0.05" Canvas.Right="-30" Canvas.Bottom="-30"/>
            <Rectangle Width="100" Height="100" Fill="#ffffff" Opacity="0.08" Canvas.Left="100" Canvas.Bottom="50">
                <Rectangle.RenderTransform>
                    <RotateTransform Angle="45"/>
                </Rectangle.RenderTransform>
            </Rectangle>
        </Canvas>
        
        <!-- المحتوى الرئيسي -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="450"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- بطاقة تسجيل الدخول -->
            <Border Grid.Column="1" 
                    Background="{StaticResource CardGradient}"
                    CornerRadius="20"
                    Effect="{StaticResource CardShadow}"
                    Margin="0,50">
                
                <StackPanel Margin="40">
                    <!-- زر الإغلاق -->
                    <Grid Margin="0,0,0,20">
                        <Button x:Name="BtnClose" 
                                Content="✕" 
                                HorizontalAlignment="Left"
                                Width="30" Height="30"
                                Background="Transparent"
                                BorderThickness="0"
                                FontSize="16"
                                Foreground="#64748b"
                                Cursor="Hand"
                                Click="BtnClose_Click"/>
                    </Grid>
                    
                    <!-- شعار وعنوان -->
                    <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                        <TextBlock Text="🏢" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock Text="مكتب الخدمات العامة" 
                                   FontSize="24" 
                                   FontWeight="Bold" 
                                   Foreground="#1e293b"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,5"/>
                        <TextBlock Text="نظام إدارة شامل ومتطور" 
                                   FontSize="14" 
                                   Foreground="#64748b"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <!-- نموذج تسجيل الدخول -->
                    <StackPanel x:Name="LoginForm">
                        <!-- اسم المستخدم -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="👤 اسم المستخدم" 
                                       FontWeight="Medium" 
                                       Foreground="#374151"
                                       Margin="0,0,0,8"/>
                            <TextBox x:Name="TxtUsername" 
                                     Style="{StaticResource LoginTextBox}"
                                     Text="هادي"/>
                        </StackPanel>
                        
                        <!-- كلمة المرور -->
                        <StackPanel Margin="0,0,0,30">
                            <TextBlock Text="🔒 كلمة المرور" 
                                       FontWeight="Medium" 
                                       Foreground="#374151"
                                       Margin="0,0,0,8"/>
                            <PasswordBox x:Name="TxtPassword" 
                                         Style="{StaticResource LoginPasswordBox}"/>
                        </StackPanel>
                        
                        <!-- زر تسجيل الدخول -->
                        <Button x:Name="BtnLogin" 
                                Content="🚀 تسجيل الدخول" 
                                Style="{StaticResource LoginButton}"
                                Click="BtnLogin_Click"
                                Margin="0,0,0,20"/>
                        
                        <!-- رسالة الخطأ -->
                        <TextBlock x:Name="TxtError" 
                                   Foreground="#dc2626"
                                   FontSize="12"
                                   HorizontalAlignment="Center"
                                   Visibility="Collapsed"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                    
                    <!-- نموذج التحقق من الرمز -->
                    <StackPanel x:Name="VerificationForm" Visibility="Collapsed">
                        <TextBlock Text="📱 تم إرسال رمز التحقق" 
                                   FontSize="16" 
                                   FontWeight="Medium"
                                   Foreground="#059669"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,10"/>
                        
                        <TextBlock Text="تم إرسال رمز التحقق المكون من 6 أرقام إلى الجوال: 0558777445"
                                   FontSize="12"
                                   Foreground="#64748b"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,10"/>

                        <TextBlock Text="⚠️ تحقق من رسائل SMS أو تطبيق الرسائل"
                                   FontSize="11"
                                   Foreground="#f59e0b"
                                   HorizontalAlignment="Center"
                                   FontWeight="Medium"
                                   Margin="0,0,0,20"/>
                        
                        <!-- رمز التحقق -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="🔢 رمز التحقق" 
                                       FontWeight="Medium" 
                                       Foreground="#374151"
                                       Margin="0,0,0,8"/>
                            <TextBox x:Name="TxtVerificationCode" 
                                     Style="{StaticResource LoginTextBox}"
                                     MaxLength="6"
                                     HorizontalContentAlignment="Center"
                                     FontSize="18"
                                     FontWeight="Bold"/>
                        </StackPanel>
                        
                        <!-- أزرار التحقق -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Button x:Name="BtnVerify" 
                                    Grid.Column="0"
                                    Content="✅ تأكيد" 
                                    Style="{StaticResource LoginButton}"
                                    Click="BtnVerify_Click"/>
                            
                            <Button x:Name="BtnResendCode" 
                                    Grid.Column="2"
                                    Content="🔄 إعادة إرسال" 
                                    Background="#6b7280"
                                    Style="{StaticResource LoginButton}"
                                    Click="BtnResendCode_Click"/>
                        </Grid>
                        
                        <!-- رسالة التحقق -->
                        <TextBlock x:Name="TxtVerificationError" 
                                   Foreground="#dc2626"
                                   FontSize="12"
                                   HorizontalAlignment="Center"
                                   Visibility="Collapsed"
                                   TextWrapping="Wrap"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                    
                    <!-- تذييل -->
                    <StackPanel HorizontalAlignment="Center" Margin="0,30,0,0">
                        <TextBlock Text="© 2024 مكتب الخدمات العامة" 
                                   FontSize="11" 
                                   Foreground="#9ca3af"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="جميع الحقوق محفوظة" 
                                   FontSize="10" 
                                   Foreground="#9ca3af"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
