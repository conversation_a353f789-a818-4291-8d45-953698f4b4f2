using System;
using System.Windows;
using System.Windows.Threading;
using System.Diagnostics;
using ArabicDashboard.Services;

namespace ArabicDashboard
{
    public partial class LoginWindow : Window
    {
        private string _correctUsername = "هادي";
        private string _correctPassword = "Ha171983$";
        private string _phoneNumber = "0558777445";
        private string _generatedCode = "";
        private DispatcherTimer _codeTimer;
        private int _codeExpirySeconds = 300; // 5 دقائق
        private readonly SmsService _smsService;

        public LoginWindow()
        {
            try
            {
                InitializeComponent();
                Debug.WriteLine("تم إنشاء نافذة تسجيل الدخول بنجاح");

                // تهيئة خدمة الرسائل النصية
                _smsService = new SmsService();

                // تركيز على حقل اسم المستخدم
                TxtUsername.Focus();

                // إعداد مؤقت انتهاء صلاحية الرمز
                _codeTimer = new DispatcherTimer();
                _codeTimer.Interval = TimeSpan.FromSeconds(1);
                _codeTimer.Tick += CodeTimer_Tick;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في إنشاء نافذة تسجيل الدخول: {ex.Message}");
                System.Windows.MessageBox.Show($"خطأ في تحميل نافذة تسجيل الدخول: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Windows.Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في إغلاق التطبيق: {ex.Message}");
            }
        }

        private void BtnLogin_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("محاولة تسجيل دخول...");
                
                // إخفاء رسالة الخطأ
                TxtError.Visibility = Visibility.Collapsed;
                
                // التحقق من البيانات
                string username = TxtUsername.Text.Trim();
                string password = TxtPassword.Password;
                
                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    TxtUsername.Focus();
                    return;
                }
                
                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    TxtPassword.Focus();
                    return;
                }
                
                // التحقق من صحة البيانات
                if (username == _correctUsername && password == _correctPassword)
                {
                    Debug.WriteLine("بيانات تسجيل الدخول صحيحة، إرسال رمز التحقق...");
                    SendVerificationCode();
                }
                else
                {
                    Debug.WriteLine("بيانات تسجيل الدخول خاطئة");
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    TxtPassword.Clear();
                    TxtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تسجيل الدخول: {ex.Message}");
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        private async void SendVerificationCode()
        {
            try
            {
                Debug.WriteLine("🔄 بدء عملية إرسال رمز التحقق...");

                // التحقق من صحة رقم الهاتف
                if (!_smsService.ValidatePhoneNumber(_phoneNumber))
                {
                    ShowError($"رقم الهاتف غير صحيح: {_phoneNumber}");
                    return;
                }

                // توليد رمز تحقق عشوائي
                Random random = new Random();
                _generatedCode = random.Next(100000, 999999).ToString();

                Debug.WriteLine($"✅ تم توليد رمز التحقق: {_generatedCode}");

                // إظهار رسالة "جاري الإرسال..."
                ShowVerificationMessage("جاري إرسال رمز التحقق...", false);

                // إرسال الرمز عبر SMS
                bool smsResult = await _smsService.SendVerificationCodeAsync(_phoneNumber, _generatedCode);

                if (smsResult)
                {
                    Debug.WriteLine($"✅ تم إرسال رمز التحقق {_generatedCode} إلى الجوال {_phoneNumber}");

                    // إخفاء نموذج تسجيل الدخول وإظهار نموذج التحقق
                    LoginForm.Visibility = Visibility.Collapsed;
                    VerificationForm.Visibility = Visibility.Visible;

                    // تركيز على حقل رمز التحقق
                    TxtVerificationCode.Focus();

                    // بدء مؤقت انتهاء صلاحية الرمز
                    _codeExpirySeconds = 300; // 5 دقائق
                    _codeTimer.Start();

                    // إظهار رسالة نجح الإرسال
                    ShowVerificationMessage($"✅ تم إرسال رمز التحقق إلى {_phoneNumber}", false);
                }
                else
                {
                    Debug.WriteLine($"❌ فشل في إرسال رمز التحقق إلى {_phoneNumber}");
                    ShowError("فشل في إرسال رمز التحقق. يرجى المحاولة مرة أخرى.");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إرسال رمز التحقق: {ex.Message}");
                ShowError($"خطأ في إرسال رمز التحقق: {ex.Message}");
            }
        }

        private void BtnVerify_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("محاولة التحقق من الرمز...");
                
                // إخفاء رسالة الخطأ
                TxtVerificationError.Visibility = Visibility.Collapsed;
                
                string enteredCode = TxtVerificationCode.Text.Trim();
                
                if (string.IsNullOrEmpty(enteredCode))
                {
                    ShowVerificationMessage("يرجى إدخال رمز التحقق", true);
                    TxtVerificationCode.Focus();
                    return;
                }
                
                if (enteredCode.Length != 6)
                {
                    ShowVerificationMessage("رمز التحقق يجب أن يكون 6 أرقام", true);
                    TxtVerificationCode.Focus();
                    return;
                }
                
                // التحقق من صحة الرمز
                if (enteredCode == _generatedCode)
                {
                    Debug.WriteLine("رمز التحقق صحيح، تسجيل دخول ناجح");
                    
                    // إيقاف المؤقت
                    _codeTimer.Stop();
                    
                    // فتح النافذة الرئيسية
                    MainWindow mainWindow = new MainWindow();
                    mainWindow.Show();
                    
                    // إغلاق نافذة تسجيل الدخول
                    this.Close();
                }
                else
                {
                    Debug.WriteLine("رمز التحقق خاطئ");
                    ShowVerificationMessage("رمز التحقق غير صحيح، يرجى المحاولة مرة أخرى", true);
                    TxtVerificationCode.Clear();
                    TxtVerificationCode.Focus();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في التحقق من الرمز: {ex.Message}");
                ShowVerificationMessage($"خطأ في التحقق: {ex.Message}", true);
            }
        }

        private async void BtnResendCode_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("🔄 إعادة إرسال رمز التحقق...");

                // إعادة تفعيل زر التحقق
                BtnVerify.IsEnabled = true;

                // إرسال رمز جديد
                SendVerificationCode();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إعادة إرسال الرمز: {ex.Message}");
                ShowVerificationMessage($"خطأ في إعادة الإرسال: {ex.Message}", true);
            }
        }

        private void CodeTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                _codeExpirySeconds--;
                
                if (_codeExpirySeconds <= 0)
                {
                    // انتهت صلاحية الرمز
                    _codeTimer.Stop();
                    _generatedCode = "";
                    
                    ShowVerificationMessage("انتهت صلاحية رمز التحقق، يرجى طلب رمز جديد", true);
                    TxtVerificationCode.Clear();
                    BtnVerify.IsEnabled = false;
                }
                else
                {
                    // تحديث رسالة الوقت المتبقي
                    int minutes = _codeExpirySeconds / 60;
                    int seconds = _codeExpirySeconds % 60;
                    
                    if (_codeExpirySeconds <= 60)
                    {
                        ShowVerificationMessage($"ينتهي الرمز خلال {seconds} ثانية", false);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في مؤقت الرمز: {ex.Message}");
            }
        }

        private void ShowError(string message)
        {
            TxtError.Text = message;
            TxtError.Visibility = Visibility.Visible;
        }

        private void ShowVerificationMessage(string message, bool isError)
        {
            TxtVerificationError.Text = message;
            TxtVerificationError.Foreground = isError ? 
                System.Windows.Media.Brushes.Red : 
                System.Windows.Media.Brushes.Green;
            TxtVerificationError.Visibility = Visibility.Visible;
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // إيقاف المؤقت عند إغلاق النافذة
                if (_codeTimer != null)
                {
                    _codeTimer.Stop();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في إغلاق النافذة: {ex.Message}");
            }
            
            base.OnClosed(e);
        }
    }
}
