﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArabicDashboard.Migrations
{
    /// <inheritdoc />
    public partial class AddInquiryCalculationTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "InquiryCalculations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ClientName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ResidenceNumber = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    ResidenceExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsEligibleForWorkPermit = table.Column<bool>(type: "INTEGER", nullable: false),
                    WorkPermitIssueDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    PassportFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EmploymentFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MedicalInsuranceFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ServiceFees = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    IsSponsorshipTransfer = table.Column<bool>(type: "INTEGER", nullable: false),
                    SponsorType = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InquiryCalculations", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_ClientName",
                table: "InquiryCalculations",
                column: "ClientName");

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_CreatedDate",
                table: "InquiryCalculations",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_InquiryCalculations_ResidenceNumber",
                table: "InquiryCalculations",
                column: "ResidenceNumber");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InquiryCalculations");
        }
    }
}
