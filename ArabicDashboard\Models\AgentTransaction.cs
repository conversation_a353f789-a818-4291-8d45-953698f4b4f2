using System;

namespace ArabicDashboard.Models
{
    public class AgentTransaction
    {
        public int Id { get; set; }
        public string AgentName { get; set; } = "";
        public string ServiceType { get; set; } = "";
        public decimal ServiceFees { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal NetProfit { get; set; }
        public string Status { get; set; } = "قيد التنفيذ";
        public string TransferStatus { get; set; } = "لم يتم التحويل";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string Notes { get; set; } = "";
    }
}
