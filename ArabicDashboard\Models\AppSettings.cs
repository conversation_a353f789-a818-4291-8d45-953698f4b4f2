namespace ArabicDashboard.Models
{
    public class AppSettings
    {
        public bool AutoStart { get; set; } = false;
        public bool NotificationsEnabled { get; set; } = true;
        public bool PrayerNotificationsEnabled { get; set; } = true;
        public bool AutoArchiveEnabled { get; set; } = true;
        public int ArchiveDay { get; set; } = 1;
        public string Theme { get; set; } = "Light";
        public string AccentColor { get; set; } = "#2563EB";
        public string FontFamily { get; set; } = "Segoe UI";
        public int FontSize { get; set; } = 14;
        public string Language { get; set; } = "Arabic";
        public bool AutoBackupEnabled { get; set; } = true;
        public string BackupInterval { get; set; } = "يومياً";
        public string BackupPath { get; set; } = @"C:\ArabicDashboard\Backups";
        public int MaxBackupFiles { get; set; } = 10;
        public bool SoundEnabled { get; set; } = true;
        public bool ShowToastNotifications { get; set; } = true;
        public int NotificationDuration { get; set; } = 5000;
        public bool DatabaseAutoBackup { get; set; } = true;
        public string DatabaseBackupInterval { get; set; } = "يومياً";
        public int ConnectionTimeout { get; set; } = 30;
        public bool EnableAnimations { get; set; } = true;
        public bool CacheEnabled { get; set; } = true;
        public int MaxCacheSize { get; set; } = 100;
        public int AutoRefreshInterval { get; set; } = 30;
        public bool RequirePasswordOnStartup { get; set; } = false;
        public bool AutoLockAfterInactivity { get; set; } = false;
        public int InactivityTimeout { get; set; } = 15;
        public bool EncryptBackups { get; set; } = false;
        public string DefaultExportFormat { get; set; } = "Excel";
        public bool IncludeChartsInExport { get; set; } = true;
        public string ExportPath { get; set; } = @"C:\ArabicDashboard\Exports";
        public bool CheckForUpdatesAutomatically { get; set; } = true;
        public string UpdateChannel { get; set; } = "Stable";
        public string LastUpdateCheck { get; set; } = "";
        public bool EnableLogging { get; set; } = true;
        public string LogLevel { get; set; } = "معلومات";
        public int MaxLogFileSize { get; set; } = 10;
        public int LogRetentionDays { get; set; } = 30;
        public bool AutoSave { get; set; } = true;
        public int MaxRecords { get; set; } = 1000;
        public bool DatabaseOptimization { get; set; } = true;
        public int DataCleanupDays { get; set; } = 90;
        public bool RememberLastSession { get; set; } = true;
        public bool SendUsageData { get; set; } = false;
        public bool NotifyNewData { get; set; } = true;
        public bool NotifyErrors { get; set; } = true;
        public bool NotifyBackup { get; set; } = true;
        public bool NotifyUpdates { get; set; } = true;
        public bool NotifyReminders { get; set; } = true;
        public bool NotifyWarnings { get; set; } = true;
    }
}
