using System;

namespace ArabicDashboard.Models
{
    public class FinancialReportItem
    {
        public DateTime Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public string ClientName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        
        public string FormattedDate => Date.ToString("yyyy-MM-dd");
        public string FormattedAmount => $"{Amount:N0} ر.س";
    }
}
