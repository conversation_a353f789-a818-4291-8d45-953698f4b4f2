using System;
using System.ComponentModel.DataAnnotations;

namespace ArabicDashboard.Models
{
    /// <summary>
    /// نموذج التذكير
    /// </summary>
    public class Reminder
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// اسم المعاملة
        /// </summary>
        [Required]
        [StringLength(200)]
        public string TransactionName { get; set; } = string.Empty;

        /// <summary>
        /// اسم العميل
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// نوع التذكير
        /// </summary>
        [Required]
        public ReminderType ReminderType { get; set; }

        /// <summary>
        /// عنوان التذكير
        /// </summary>
        [Required]
        [StringLength(150)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// وصف التذكير
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ ووقت التذكير
        /// </summary>
        [Required]
        public DateTime ReminderDateTime { get; set; }

        /// <summary>
        /// هل تم تنفيذ التذكير
        /// </summary>
        public bool IsCompleted { get; set; } = false;

        /// <summary>
        /// هل التذكير نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// أولوية التذكير
        /// </summary>
        public ReminderPriority Priority { get; set; } = ReminderPriority.Medium;

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(300)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف للتذكير (اختياري)
        /// </summary>
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// البريد الإلكتروني للتذكير (اختياري)
        /// </summary>
        [StringLength(100)]
        public string? Email { get; set; }

        // خصائص محسوبة للعرض
        public string ReminderTypeText => GetReminderTypeText();
        public string PriorityText => GetPriorityText();
        public string StatusText => IsCompleted ? "مكتمل" : (IsActive ? "نشط" : "معطل");
        public string ReminderDateTimeFormatted => ReminderDateTime.ToString("yyyy/MM/dd HH:mm");
        public string CreatedDateFormatted => CreatedDate.ToString("yyyy/MM/dd");
        public bool IsOverdue => !IsCompleted && ReminderDateTime < DateTime.Now;
        public string OverdueText => IsOverdue ? "متأخر" : "";

        private string GetReminderTypeText()
        {
            return ReminderType switch
            {
                ReminderType.PaymentDue => "استحقاق دفع",
                ReminderType.DocumentExpiry => "انتهاء وثيقة",
                ReminderType.FollowUp => "متابعة",
                ReminderType.Appointment => "موعد",
                ReminderType.Renewal => "تجديد",
                ReminderType.Delivery => "تسليم",
                ReminderType.Meeting => "اجتماع",
                ReminderType.Call => "مكالمة",
                ReminderType.Email => "بريد إلكتروني",
                ReminderType.Visit => "زيارة",
                ReminderType.Other => "أخرى",
                _ => "غير محدد"
            };
        }

        private string GetPriorityText()
        {
            return Priority switch
            {
                ReminderPriority.Low => "منخفضة",
                ReminderPriority.Medium => "متوسطة",
                ReminderPriority.High => "عالية",
                ReminderPriority.Urgent => "عاجل",
                _ => "متوسطة"
            };
        }
    }

    /// <summary>
    /// أنواع التذكير
    /// </summary>
    public enum ReminderType
    {
        PaymentDue = 1,      // استحقاق دفع
        DocumentExpiry = 2,   // انتهاء وثيقة
        FollowUp = 3,        // متابعة
        Appointment = 4,     // موعد
        Renewal = 5,         // تجديد
        Delivery = 6,        // تسليم
        Meeting = 7,         // اجتماع
        Call = 8,            // مكالمة
        Email = 9,           // بريد إلكتروني
        Visit = 10,          // زيارة
        Other = 11           // أخرى
    }

    /// <summary>
    /// أولوية التذكير
    /// </summary>
    public enum ReminderPriority
    {
        Low = 1,      // منخفضة
        Medium = 2,   // متوسطة
        High = 3,     // عالية
        Urgent = 4    // عاجل
    }
}
