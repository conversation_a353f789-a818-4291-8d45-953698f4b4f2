using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace ArabicDashboard.Models
{
    /// <summary>
    /// نموذج بيانات الإقامة للتحقق الدقيق
    /// </summary>
    public class ResidenceInfo
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// رقم الإقامة
        /// </summary>
        [Required]
        [StringLength(20)]
        public string ResidenceNumber { get; set; } = string.Empty;

        /// <summary>
        /// اسم صاحب الإقامة
        /// </summary>
        [Required]
        [StringLength(100)]
        public string HolderName { get; set; } = string.Empty;

        /// <summary>
        /// الجنسية
        /// </summary>
        [StringLength(50)]
        public string Nationality { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إصدار الإقامة
        /// </summary>
        [Required]
        public DateTime IssueDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء الإقامة
        /// </summary>
        [Required]
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// حالة الإقامة
        /// </summary>
        public ResidenceStatus Status { get; set; } = ResidenceStatus.Active;

        /// <summary>
        /// نوع الإقامة
        /// </summary>
        [StringLength(50)]
        public string ResidenceType { get; set; } = string.Empty;

        /// <summary>
        /// اسم الكفيل
        /// </summary>
        [StringLength(100)]
        public string SponsorName { get; set; } = string.Empty;

        /// <summary>
        /// رقم هوية الكفيل
        /// </summary>
        [StringLength(20)]
        public string SponsorId { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ آخر تحديث للبيانات
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// مصدر البيانات (يدوي، API، إلخ)
        /// </summary>
        [StringLength(50)]
        public string DataSource { get; set; } = "Manual";

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        // خصائص محسوبة للعرض
        public string ExpiryDateFormatted => ExpiryDate.ToString("yyyy/MM/dd");
        public string IssueDateFormatted => IssueDate.ToString("yyyy/MM/dd");
        public string ExpiryDateHijri => ConvertToHijri(ExpiryDate);
        public string IssueDateHijri => ConvertToHijri(IssueDate);
        public string StatusText => GetStatusText();
        public int DaysUntilExpiry => (ExpiryDate - DateTime.Now).Days;
        public bool IsExpired => ExpiryDate < DateTime.Now;
        public bool IsExpiringSoon => DaysUntilExpiry <= 30 && DaysUntilExpiry > 0;
        public bool IsEligibleForWorkPermit => DaysUntilExpiry > 30;

        /// <summary>
        /// تحويل التاريخ الميلادي إلى هجري
        /// </summary>
        private string ConvertToHijri(DateTime gregorianDate)
        {
            try
            {
                var hijriCalendar = new UmAlQuraCalendar();
                var hijriYear = hijriCalendar.GetYear(gregorianDate);
                var hijriMonth = hijriCalendar.GetMonth(gregorianDate);
                var hijriDay = hijriCalendar.GetDayOfMonth(gregorianDate);
                
                return $"{hijriDay:00}/{hijriMonth:00}/{hijriYear}";
            }
            catch
            {
                return "غير متاح";
            }
        }

        /// <summary>
        /// الحصول على نص حالة الإقامة
        /// </summary>
        private string GetStatusText()
        {
            return Status switch
            {
                ResidenceStatus.Active => "سارية",
                ResidenceStatus.Expired => "منتهية الصلاحية",
                ResidenceStatus.Cancelled => "ملغية",
                ResidenceStatus.Suspended => "معلقة",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// حساب تاريخ سحب رخصة العمل المقترح
        /// </summary>
        public DateTime? GetSuggestedWorkPermitDate()
        {
            if (!IsEligibleForWorkPermit)
                return null;

            // اقتراح تاريخ سحب رخصة العمل بعد أسبوع من الآن
            // مع مراعاة عدم تجاوز تاريخ انتهاء الإقامة بـ 60 يوم
            var suggestedDate = DateTime.Now.AddDays(7);
            var maxAllowedDate = ExpiryDate.AddDays(-60);
            
            return suggestedDate <= maxAllowedDate ? suggestedDate : maxAllowedDate;
        }

        /// <summary>
        /// التحقق من صحة بيانات الإقامة
        /// </summary>
        public ValidationResult ValidateResidence()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(ResidenceNumber))
            {
                result.AddError("رقم الإقامة مطلوب");
            }
            else if (ResidenceNumber.Length < 10)
            {
                result.AddError("رقم الإقامة يجب أن يكون 10 أرقام على الأقل");
            }

            if (string.IsNullOrWhiteSpace(HolderName))
            {
                result.AddError("اسم صاحب الإقامة مطلوب");
            }

            if (IssueDate >= ExpiryDate)
            {
                result.AddError("تاريخ الإصدار يجب أن يكون قبل تاريخ الانتهاء");
            }

            if (ExpiryDate < DateTime.Now.AddDays(-365))
            {
                result.AddError("تاريخ انتهاء الإقامة قديم جداً");
            }

            return result;
        }
    }

    /// <summary>
    /// حالة الإقامة
    /// </summary>
    public enum ResidenceStatus
    {
        Active = 1,      // سارية
        Expired = 2,     // منتهية الصلاحية
        Cancelled = 3,   // ملغية
        Suspended = 4    // معلقة
    }

    /// <summary>
    /// نتيجة التحقق من صحة البيانات
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid => Errors.Count == 0;
        public List<string> Errors { get; set; } = new List<string>();

        public void AddError(string error)
        {
            Errors.Add(error);
        }

        public string GetErrorsText()
        {
            return string.Join("\n", Errors);
        }
    }

    /// <summary>
    /// معلومات رخصة العمل
    /// </summary>
    public class WorkPermitInfo
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// رقم الإقامة المرتبط
        /// </summary>
        [Required]
        [StringLength(20)]
        public string ResidenceNumber { get; set; } = string.Empty;

        /// <summary>
        /// رقم رخصة العمل
        /// </summary>
        [StringLength(20)]
        public string WorkPermitNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إصدار رخصة العمل
        /// </summary>
        public DateTime? IssueDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء رخصة العمل
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// حالة رخصة العمل
        /// </summary>
        public WorkPermitStatus Status { get; set; } = WorkPermitStatus.NotIssued;

        /// <summary>
        /// اسم صاحب العمل
        /// </summary>
        [StringLength(100)]
        public string EmployerName { get; set; } = string.Empty;

        /// <summary>
        /// نوع العمل
        /// </summary>
        [StringLength(100)]
        public string JobType { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        // خصائص محسوبة
        public string IssueDateFormatted => IssueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
        public string ExpiryDateFormatted => ExpiryDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
        public string IssueDateHijri => IssueDate.HasValue ? ConvertToHijri(IssueDate.Value) : "غير محدد";
        public string ExpiryDateHijri => ExpiryDate.HasValue ? ConvertToHijri(ExpiryDate.Value) : "غير محدد";
        public string StatusText => GetStatusText();

        private string ConvertToHijri(DateTime gregorianDate)
        {
            try
            {
                var hijriCalendar = new UmAlQuraCalendar();
                var hijriYear = hijriCalendar.GetYear(gregorianDate);
                var hijriMonth = hijriCalendar.GetMonth(gregorianDate);
                var hijriDay = hijriCalendar.GetDayOfMonth(gregorianDate);
                
                return $"{hijriDay:00}/{hijriMonth:00}/{hijriYear}";
            }
            catch
            {
                return "غير متاح";
            }
        }

        private string GetStatusText()
        {
            return Status switch
            {
                WorkPermitStatus.NotIssued => "لم تصدر",
                WorkPermitStatus.Active => "سارية",
                WorkPermitStatus.Expired => "منتهية الصلاحية",
                WorkPermitStatus.Cancelled => "ملغية",
                _ => "غير محدد"
            };
        }
    }

    /// <summary>
    /// حالة رخصة العمل
    /// </summary>
    public enum WorkPermitStatus
    {
        NotIssued = 0,   // لم تصدر
        Active = 1,      // سارية
        Expired = 2,     // منتهية الصلاحية
        Cancelled = 3    // ملغية
    }
}
