using System;

namespace ArabicDashboard.Models
{
    public class SimpleTransaction
    {
        public int Id { get; set; }
        public string ClientName { get; set; } = "";
        public string ServiceType { get; set; } = "";
        public decimal ServiceFees { get; set; }
        public decimal GovernmentFees { get; set; }
        public decimal LaborOfficeFees { get; set; }
        public decimal NetProfit { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; } = "قيد التنفيذ";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string Notes { get; set; } = "";
        public bool IsEmployed { get; set; } = false;
        public decimal EmploymentAmount { get; set; } = 0;
    }
}
