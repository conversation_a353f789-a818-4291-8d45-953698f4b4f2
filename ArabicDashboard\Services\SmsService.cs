using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Linq;

namespace ArabicDashboard.Services
{
    public class SmsService
    {
        private static readonly HttpClient httpClient = new HttpClient();
        private readonly string _apiKey = "your-sms-api-key"; // يجب استبدالها بمفتاح API حقيقي
        private readonly string _senderName = "مكتب الخدمات";

        public async Task<bool> SendVerificationCodeAsync(string phoneNumber, string verificationCode)
        {
            try
            {
                Debug.WriteLine($"🔄 محاولة إرسال رمز التحقق {verificationCode} إلى الرقم {phoneNumber}");

                // في البيئة التطويرية، نحاكي إرسال الرسالة
                if (IsTestEnvironment())
                {
                    return await SimulateSmsDelivery(phoneNumber, verificationCode);
                }

                // في البيئة الإنتاجية، استخدم خدمة SMS حقيقية
                return await SendRealSms(phoneNumber, verificationCode);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إرسال رمز التحقق: {ex.Message}");
                return false;
            }
        }

        private bool IsTestEnvironment()
        {
            // تحديد ما إذا كنا في بيئة تطوير أم إنتاج
            return true; // مؤقتاً للتطوير
        }

        private async Task<bool> SimulateSmsDelivery(string phoneNumber, string verificationCode)
        {
            try
            {
                Debug.WriteLine($"📱 محاكاة إرسال SMS:");
                Debug.WriteLine($"   📞 الرقم: {phoneNumber}");
                Debug.WriteLine($"   🔢 الرمز: {verificationCode}");
                Debug.WriteLine($"   📝 الرسالة: رمز التحقق الخاص بك هو: {verificationCode}");
                Debug.WriteLine($"   🏢 المرسل: {_senderName}");

                // محاكاة تأخير الشبكة
                await Task.Delay(2000);

                // محاكاة نجاح الإرسال
                Debug.WriteLine("✅ تم إرسال الرسالة بنجاح (محاكاة)");
                
                // عرض الرمز في نافذة منبثقة للتطوير
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    System.Windows.MessageBox.Show(
                        $"رمز التحقق (للتطوير):\n\n{verificationCode}\n\nتم إرساله إلى: {phoneNumber}",
                        "رمز التحقق - بيئة التطوير",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information
                    );
                });

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في محاكاة الإرسال: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> SendRealSms(string phoneNumber, string verificationCode)
        {
            try
            {
                // تنسيق رقم الهاتف (إزالة الأصفار والمسافات)
                string formattedPhone = FormatPhoneNumber(phoneNumber);
                
                // إعداد الرسالة
                string message = $"رمز التحقق الخاص بك هو: {verificationCode}\nمن: {_senderName}";

                // استخدام خدمة SMS حقيقية (مثل Twilio, AWS SNS, أو خدمة محلية)
                var result = await SendViaTwilio(formattedPhone, message);
                
                if (!result)
                {
                    // محاولة خدمة بديلة
                    result = await SendViaAlternativeService(formattedPhone, message);
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إرسال SMS حقيقي: {ex.Message}");
                return false;
            }
        }

        private string FormatPhoneNumber(string phoneNumber)
        {
            // إزالة المسافات والرموز الخاصة
            string cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
            
            // إضافة رمز الدولة السعودية إذا لم يكن موجوداً
            if (cleaned.StartsWith("05"))
            {
                cleaned = "+966" + cleaned.Substring(1);
            }
            else if (cleaned.StartsWith("5"))
            {
                cleaned = "+966" + cleaned;
            }
            else if (!cleaned.StartsWith("+966"))
            {
                cleaned = "+966" + cleaned;
            }

            return cleaned;
        }

        private async Task<bool> SendViaTwilio(string phoneNumber, string message)
        {
            try
            {
                // هنا يمكن إضافة كود Twilio الحقيقي
                Debug.WriteLine($"🔄 محاولة الإرسال عبر Twilio إلى {phoneNumber}");
                
                // مثال على استخدام Twilio API
                /*
                var accountSid = "your_account_sid";
                var authToken = "your_auth_token";
                TwilioClient.Init(accountSid, authToken);

                var messageResource = await MessageResource.CreateAsync(
                    body: message,
                    from: new PhoneNumber("+**********"), // رقم Twilio
                    to: new PhoneNumber(phoneNumber)
                );

                return messageResource.Status == MessageResource.StatusEnum.Sent;
                */

                await Task.Delay(1000); // محاكاة
                return false; // لأننا لا نملك مفاتيح API حقيقية
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في Twilio: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> SendViaAlternativeService(string phoneNumber, string message)
        {
            try
            {
                // خدمة SMS بديلة (مثل خدمة محلية سعودية)
                Debug.WriteLine($"🔄 محاولة الإرسال عبر خدمة بديلة إلى {phoneNumber}");

                // مثال على استخدام خدمة محلية
                var requestData = new
                {
                    phone = phoneNumber,
                    message = message,
                    sender = _senderName
                };

                var json = System.Text.Json.JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // إضافة headers المطلوبة
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

                // إرسال الطلب (URL وهمي)
                var response = await httpClient.PostAsync("https://api.sms-service.com/send", content);
                
                if (response.IsSuccessStatusCode)
                {
                    Debug.WriteLine("✅ تم إرسال الرسالة عبر الخدمة البديلة");
                    return true;
                }
                else
                {
                    Debug.WriteLine($"❌ فشل الإرسال عبر الخدمة البديلة: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في الخدمة البديلة: {ex.Message}");
                return false;
            }
        }

        public bool ValidatePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // تنظيف الرقم
            string cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // التحقق من صحة الرقم السعودي
            if (cleaned.StartsWith("+966"))
            {
                cleaned = cleaned.Substring(4);
            }
            else if (cleaned.StartsWith("966"))
            {
                cleaned = cleaned.Substring(3);
            }
            else if (cleaned.StartsWith("0"))
            {
                cleaned = cleaned.Substring(1);
            }

            // يجب أن يبدأ بـ 5 ويكون 9 أرقام
            return cleaned.StartsWith("5") && cleaned.Length == 9 && cleaned.All(char.IsDigit);
        }
    }
}
