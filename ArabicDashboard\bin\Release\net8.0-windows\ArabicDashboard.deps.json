{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ArabicDashboard/1.0.0": {"dependencies": {"BouncyCastle.NetCore": "2.2.1", "DevExpress.WPF.ProjectTemplates": "25.1.1-alpha", "DevExpress.Wpf.Charts": "24.1.7", "DevExpress.Wpf.Grid": "24.1.7", "DevExpress.Wpf.Themes.All": "24.1.7", "Microsoft.EntityFrameworkCore.Design": "9.0.5", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.5", "Microsoft.Web.WebView2": "1.0.2210.55", "System.Drawing.Common": "8.0.0", "iTextSharp.LGPLv2.Core": "3.7.4", "itext7": "9.2.0"}, "runtime": {"ArabicDashboard.dll": {}}}, "BouncyCastle.Cryptography/2.6.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.6.1.59591"}}}, "BouncyCastle.NetCore/2.2.1": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "DevExpress.Charts.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7"}, "runtime": {"lib/net6.0/DevExpress.Charts.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.CodeParser/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/DevExpress.CodeParser.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Data/24.1.7": {"dependencies": {"System.Drawing.Common": "8.0.0"}, "runtime": {"lib/net6.0/DevExpress.Data.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Data.Desktop/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Data.Desktop.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.DataAccess/24.1.7": {"dependencies": {"DevExpress.CodeParser": "24.1.7", "DevExpress.Data": "24.1.7", "DevExpress.Office.Core": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.RichEdit.Core": "24.1.7", "DevExpress.Xpo": "24.1.7", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.SqlClient": "4.8.6"}, "runtime": {"lib/net6.0/DevExpress.DataAccess.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.DataVisualization.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7"}, "runtime": {"lib/net6.0/DevExpress.DataVisualization.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Drawing/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "System.Drawing.Common": "8.0.0"}, "runtime": {"lib/net6.0/DevExpress.Drawing.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Images/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "System.Drawing.Common": "8.0.0"}, "runtime": {"lib/net6.0/DevExpress.Images.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Mvvm/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Mvvm.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Office.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Pdf.Core": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "System.Drawing.Common": "8.0.0"}, "runtime": {"lib/net6.0/DevExpress.Office.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Pdf.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "System.Drawing.Common": "8.0.0", "System.Formats.Asn1": "6.0.1", "System.Security.Cryptography.Pkcs": "6.0.3"}, "runtime": {"lib/net6.0/DevExpress.Pdf.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Pdf.Drawing/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Pdf.Core": "24.1.7", "System.Drawing.Common": "8.0.0"}, "runtime": {"lib/net6.0/DevExpress.Pdf.v24.1.Drawing.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Printing.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Pdf.Core": "24.1.7", "DevExpress.Pdf.Drawing": "24.1.7", "System.Drawing.Common": "8.0.0", "System.Formats.Asn1": "6.0.1", "System.Security.Cryptography.Pkcs": "6.0.3", "System.ServiceModel.Http": "6.2.0"}, "runtime": {"lib/net6.0/DevExpress.Printing.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.RichEdit.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Office.Core": "24.1.7", "DevExpress.Pdf.Core": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "System.Drawing.Common": "8.0.0"}, "runtime": {"lib/net6.0/DevExpress.RichEdit.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Charts/24.1.7": {"dependencies": {"DevExpress.Charts.Core": "24.1.7", "DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.DataVisualization.Core": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.Wpf.Core": "24.1.7", "DevExpress.Wpf.Docking": "24.1.7", "DevExpress.Wpf.Grid.Core": "24.1.7", "DevExpress.Wpf.Printing": "24.1.7", "DevExpress.Wpf.PropertyGrid": "24.1.7", "DevExpress.Wpf.Ribbon": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Charts.Designer.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}, "lib/net6.0-windows/DevExpress.Xpf.Charts.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Controls/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.Wpf.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Controls.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Pdf.Core": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.Wpf.Themes.Office2019Colorful": "24.1.7", "System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Core.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Docking/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Wpf.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Docking.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}, "lib/net6.0-windows/DevExpress.Xpf.Layout.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.DocumentViewer.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Wpf.Controls": "24.1.7", "DevExpress.Wpf.Core": "24.1.7", "DevExpress.Wpf.Grid.Core": "24.1.7", "DevExpress.Wpf.Ribbon": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.DocumentViewer.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.ExpressionEditor/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.DataAccess": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.RichEdit.Core": "24.1.7", "DevExpress.Wpf.Core": "24.1.7", "DevExpress.Wpf.RichEdit": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.ExpressionEditor.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Grid/24.1.7": {"dependencies": {"DevExpress.Wpf.ExpressionEditor": "24.1.7", "DevExpress.Wpf.Grid.Printing": "24.1.7"}}, "DevExpress.Wpf.Grid.Core/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.Wpf.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Grid.v24.1.Core.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}, "lib/net6.0-windows/DevExpress.Xpf.Grid.v24.1.Extensions.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}, "lib/net6.0-windows/DevExpress.Xpf.Grid.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Grid.Printing/24.1.7": {"dependencies": {"DevExpress.Wpf.Grid.Core": "24.1.7", "DevExpress.Wpf.Printing": "24.1.7"}}, "DevExpress.Wpf.LayoutControl/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Wpf.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.LayoutControl.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Office/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Office.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Office.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Printing/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Office.Core": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.RichEdit.Core": "24.1.7", "DevExpress.Wpf.Controls": "24.1.7", "DevExpress.Wpf.Core": "24.1.7", "DevExpress.Wpf.Docking": "24.1.7", "DevExpress.Wpf.DocumentViewer.Core": "24.1.7", "DevExpress.Wpf.Grid.Core": "24.1.7", "DevExpress.Wpf.LayoutControl": "24.1.7", "DevExpress.Wpf.Ribbon": "24.1.7", "System.ServiceModel.Http": "6.2.0"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Printing.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.WPF.ProjectTemplates/25.1.1-alpha": {}, "DevExpress.Wpf.PropertyGrid/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.Wpf.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.PropertyGrid.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Ribbon/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Wpf.Core": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.Ribbon.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.RichEdit/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "DevExpress.Data.Desktop": "24.1.7", "DevExpress.Drawing": "24.1.7", "DevExpress.Images": "24.1.7", "DevExpress.Mvvm": "24.1.7", "DevExpress.Office.Core": "24.1.7", "DevExpress.Pdf.Core": "24.1.7", "DevExpress.Printing.Core": "24.1.7", "DevExpress.RichEdit.Core": "24.1.7", "DevExpress.Wpf.Core": "24.1.7", "DevExpress.Wpf.Docking": "24.1.7", "DevExpress.Wpf.Office": "24.1.7", "DevExpress.Wpf.Printing": "24.1.7", "DevExpress.Wpf.Ribbon": "24.1.7"}, "runtime": {"lib/net6.0-windows/DevExpress.Xpf.RichEdit.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.All/24.1.7": {"dependencies": {"DevExpress.Wpf.Themes.DXStyle": "24.1.7", "DevExpress.Wpf.Themes.LightGray": "24.1.7", "DevExpress.Wpf.Themes.MetropolisDark": "24.1.7", "DevExpress.Wpf.Themes.MetropolisLight": "24.1.7", "DevExpress.Wpf.Themes.Office2007Black": "24.1.7", "DevExpress.Wpf.Themes.Office2007Blue": "24.1.7", "DevExpress.Wpf.Themes.Office2007Silver": "24.1.7", "DevExpress.Wpf.Themes.Office2010Black": "24.1.7", "DevExpress.Wpf.Themes.Office2010Blue": "24.1.7", "DevExpress.Wpf.Themes.Office2010Silver": "24.1.7", "DevExpress.Wpf.Themes.Office2013": "24.1.7", "DevExpress.Wpf.Themes.Office2013DarkGray": "24.1.7", "DevExpress.Wpf.Themes.Office2013LightGray": "24.1.7", "DevExpress.Wpf.Themes.Office2016Black": "24.1.7", "DevExpress.Wpf.Themes.Office2016BlackSE": "24.1.7", "DevExpress.Wpf.Themes.Office2016Colorful": "24.1.7", "DevExpress.Wpf.Themes.Office2016ColorfulSE": "24.1.7", "DevExpress.Wpf.Themes.Office2016DarkGraySE": "24.1.7", "DevExpress.Wpf.Themes.Office2016White": "24.1.7", "DevExpress.Wpf.Themes.Office2016WhiteSE": "24.1.7", "DevExpress.Wpf.Themes.Office2019Black": "24.1.7", "DevExpress.Wpf.Themes.Office2019Colorful": "24.1.7", "DevExpress.Wpf.Themes.Office2019DarkGray": "24.1.7", "DevExpress.Wpf.Themes.Office2019HighContrast": "24.1.7", "DevExpress.Wpf.Themes.Office2019White": "24.1.7", "DevExpress.Wpf.Themes.Seven": "24.1.7", "DevExpress.Wpf.Themes.TouchlineDark": "24.1.7", "DevExpress.Wpf.Themes.VS2010": "24.1.7", "DevExpress.Wpf.Themes.VS2017Blue": "24.1.7", "DevExpress.Wpf.Themes.VS2017Dark": "24.1.7", "DevExpress.Wpf.Themes.VS2017Light": "24.1.7", "DevExpress.Wpf.Themes.VS2019Blue": "24.1.7", "DevExpress.Wpf.Themes.VS2019Dark": "24.1.7", "DevExpress.Wpf.Themes.VS2019Light": "24.1.7", "DevExpress.Wpf.Themes.Win10Dark": "24.1.7", "DevExpress.Wpf.Themes.Win10Light": "24.1.7", "DevExpress.Wpf.Themes.Win11Dark": "24.1.7", "DevExpress.Wpf.Themes.Win11Light": "24.1.7"}}, "DevExpress.Wpf.Themes.DXStyle/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.DXStyle.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.LightGray/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.LightGray.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.MetropolisDark/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.MetropolisDark.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.MetropolisLight/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.MetropolisLight.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2007Black/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2007Black.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2007Blue/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2007Blue.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2007Silver/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2007Silver.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2010Black/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2010Black.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2010Blue/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2010Blue.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2010Silver/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2010Silver.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2013/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2013.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2013DarkGray/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2013DarkGray.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2013LightGray/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2013LightGray.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016Black/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016Black.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016BlackSE/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016BlackSE.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016Colorful/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016Colorful.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016ColorfulSE/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016ColorfulSE.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016DarkGraySE/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016DarkGraySE.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016White/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016White.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2016WhiteSE/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2016WhiteSE.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2019Black/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2019Black.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2019Colorful/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2019DarkGray/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2019DarkGray.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2019HighContrast/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2019HighContrast.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Office2019White/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Office2019White.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Seven/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Seven.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.TouchlineDark/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.TouchlineDark.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2010/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2010.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2017Blue/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2017Blue.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2017Dark/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2017Dark.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2017Light/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2017Light.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2019Blue/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2019Blue.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2019Dark/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2019Dark.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.VS2019Light/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.VS2019Light.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Win10Dark/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Win10Dark.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Win10Light/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Win10Light.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Win11Dark/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Win11Dark.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Wpf.Themes.Win11Light/24.1.7": {"runtime": {"lib/net6.0-windows/DevExpress.Xpf.Themes.Win11Light.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "DevExpress.Xpo/24.1.7": {"dependencies": {"DevExpress.Data": "24.1.7", "Microsoft.Extensions.DependencyInjection": "9.0.5", "System.Data.SqlClient": "4.8.6", "System.Drawing.Common": "8.0.0", "System.Formats.Asn1": "6.0.1", "System.Security.Cryptography.Pkcs": "6.0.3", "System.ServiceModel.Http": "6.2.0", "System.ServiceModel.NetTcp": "6.2.0"}, "runtime": {"lib/net6.0/DevExpress.Xpo.v24.1.dll": {"assemblyVersion": "24.1.7.0", "fileVersion": "24.1.7.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "itext/9.2.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "1.1.0", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Process": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Text.Encoding.CodePages": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "itext.commons": "9.2.0"}, "runtime": {"lib/netstandard2.0/itext.barcodes.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.bouncy-castle-connector.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.forms.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.io.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.kernel.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.layout.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.pdfa.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.pdfua.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.sign.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.styledxmlparser.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}, "lib/netstandard2.0/itext.svg.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}}}, "itext.commons/9.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"lib/netstandard2.0/itext.commons.dll": {"assemblyVersion": "9.2.0.0", "fileVersion": "9.2.0.0"}}}, "itext7/9.2.0": {"dependencies": {"itext": "9.2.0"}}, "iTextSharp.LGPLv2.Core/3.7.4": {"dependencies": {"BouncyCastle.Cryptography": "2.6.1", "SkiaSharp": "3.119.0"}, "runtime": {"lib/net8.0/iTextSharp.LGPLv2.Core.dll": {"assemblyVersion": "3.7.4.0", "fileVersion": "3.7.4.0"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "9.0.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/9.0.5": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.5", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {}, "Microsoft.EntityFrameworkCore.Design/9.0.5": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.5"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.5": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.5", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyModel/9.0.5": {"dependencies": {"System.Text.Encodings.Web": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.5", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.ObjectPool/6.0.16": {"runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1623.17406"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Web.WebView2/1.0.2210.55": {"runtime": {"lib/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.2210.55", "fileVersion": "1.0.2210.55"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.2210.55", "fileVersion": "1.0.2210.55"}, "lib/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.2210.55", "fileVersion": "1.0.2210.55"}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.2210.55"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.2210.55"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.2210.55"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/8.0.0": {}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SkiaSharp/3.119.0": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "3.119.0", "SkiaSharp.NativeAssets.macOS": "3.119.0"}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"assemblyVersion": "3.119.0.0", "fileVersion": "3.119.0.0"}}}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.CodeDom/6.0.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/7.0.0": {}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.7.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Drawing.Common/8.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}}, "System.Formats.Asn1/6.0.1": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Pipelines/9.0.5": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Memory/4.5.3": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/6.0.3": {"dependencies": {"System.Formats.Asn1": "6.0.1"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.3"}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.ServiceModel.Http/6.2.0": {"dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetFramingBase/6.2.0": {"dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetTcp/6.2.0": {"dependencies": {"System.ServiceModel.NetFramingBase": "6.2.0", "System.ServiceModel.Primitives": "6.2.0"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/6.2.0": {"dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.200.23.55602"}}, "resources": {"lib/net6.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/9.0.5": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Text.Json/9.0.5": {"dependencies": {"System.IO.Pipelines": "9.0.5", "System.Text.Encodings.Web": "9.0.5"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "8.0.0"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}}}, "libraries": {"ArabicDashboard/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BouncyCastle.Cryptography/2.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-vZsG2YILhthgRqO+ZVgRff4ZFKKTl0v7kqaVBLCtRvpREhfBP33pcWrdA3PRYgWuFL1RxiUFvjMUHTdBZlJcoA==", "path": "bouncycastle.cryptography/2.6.1", "hashPath": "bouncycastle.cryptography.2.6.1.nupkg.sha512"}, "BouncyCastle.NetCore/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-yfWn8JYPc4rkeM2kcsCqFVFOvwCuuQvIieGtQWcjoWxOioeznXQB3M/GmHgbCWbJjc8ycrwGhZaZPiasifYi4A==", "path": "bouncycastle.netcore/2.2.1", "hashPath": "bouncycastle.netcore.2.2.1.nupkg.sha512"}, "DevExpress.Charts.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-3UBfqwQ/z5d4f6OV4aUjDTQH12XeFPnB0y1LMQHF6xy4fVEKYrQpgQA4MFFCNdv4GUDMppU8x+iOurmaM8uWag==", "path": "devexpress.charts.core/24.1.7", "hashPath": "devexpress.charts.core.24.1.7.nupkg.sha512"}, "DevExpress.CodeParser/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-W6t9sjkDvckEK7TUtOHWX6i4xNbVbDEKHJDvGMC5aG9LPXZ7sVMhYiy1kJTwhDVPNaGMlgFr4vhnK4DYMPWX8g==", "path": "devexpress.codeparser/24.1.7", "hashPath": "devexpress.codeparser.24.1.7.nupkg.sha512"}, "DevExpress.Data/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-5nOCBKGf4GZbw3G4+/feH8xbahLAgJVegLR6iNh+w/c6JTEJT9vmsIDyuSBz0AZjic4DHkaEg+DN7iZLI9jPGg==", "path": "devexpress.data/24.1.7", "hashPath": "devexpress.data.24.1.7.nupkg.sha512"}, "DevExpress.Data.Desktop/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-G7DpxW7YdFM9/wVyV25Wy5TSNIqn2hnSZqbA7HDsW7sKEsUlwaQCw1icFOSg053UxzzXorF9fyuD0WlJtjwUtA==", "path": "devexpress.data.desktop/24.1.7", "hashPath": "devexpress.data.desktop.24.1.7.nupkg.sha512"}, "DevExpress.DataAccess/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-lSyi57tBuHh2yJiXYvV3p+c3M+vn1sYRJHZy6Uj6HrFU4og75M/d1ZGHbkY87xkb/XT6GUlUfXvJ7R//JvutRQ==", "path": "devexpress.dataaccess/24.1.7", "hashPath": "devexpress.dataaccess.24.1.7.nupkg.sha512"}, "DevExpress.DataVisualization.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-kKoWtfOML5jL+biX9CuZOdN/FvEUad5vvvB2iU1Z3E2bfYALAl+d8TlZzDv1dD43qZlXznwj1BNPRiZraAqtog==", "path": "devexpress.datavisualization.core/24.1.7", "hashPath": "devexpress.datavisualization.core.24.1.7.nupkg.sha512"}, "DevExpress.Drawing/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-jyFsCM0H09W8ZP4GlMl8I6m8Z+l5xRMIlM8nipomN9qlytYhSiDlNjnFHJQhsDZegz2OvkFDi6DJZaqiBtbC6A==", "path": "devexpress.drawing/24.1.7", "hashPath": "devexpress.drawing.24.1.7.nupkg.sha512"}, "DevExpress.Images/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-FuGN0IuW37aWDRavwiA8HN28UoMqAa+q8GAdYAR6T982Eq2B87UU5wNk/u7WA9UVwe6FkTRgJUnzQdDpY6aPaw==", "path": "devexpress.images/24.1.7", "hashPath": "devexpress.images.24.1.7.nupkg.sha512"}, "DevExpress.Mvvm/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-H3N6goa0V0j9WpDgPnNcgGi73s8mqVdKjuD8ntSkhzqDjHfMm+L12Nm+vd64dq6HSVTnmbrLui2lWczp33hPhw==", "path": "devexpress.mvvm/24.1.7", "hashPath": "devexpress.mvvm.24.1.7.nupkg.sha512"}, "DevExpress.Office.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-QLWlYVFm5quBUtWnZalhuBQBjKylX5CbKdxZ7tLMwbHWUMGbZyAQRThHnvYOC0PjOdIfXl2o+Tv8y4aW65+AFg==", "path": "devexpress.office.core/24.1.7", "hashPath": "devexpress.office.core.24.1.7.nupkg.sha512"}, "DevExpress.Pdf.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-BeAfsjlt6I+2EEgfpYlgTxck2FvkmlnCeBxiO2Irl7VuHoJ5rFhIChVnqxTitkCzniVypltDWtihps2WcJxz7w==", "path": "devexpress.pdf.core/24.1.7", "hashPath": "devexpress.pdf.core.24.1.7.nupkg.sha512"}, "DevExpress.Pdf.Drawing/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-UAR7S7MJ+4tyicL1QXDNOfk9ErU74Z/kYThl1Rud7RS7cGDq9lwLRVW1laHzJxiMbig7G6A/sTqDUqAugVTa1g==", "path": "devexpress.pdf.drawing/24.1.7", "hashPath": "devexpress.pdf.drawing.24.1.7.nupkg.sha512"}, "DevExpress.Printing.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-MiLs4bvWJ6AEB8ZUqu8K1sQgxPkAoKCVauF+YWzINvSRgv0MnATjMClLPLTxAULIx/fL04dEo4D4TfrdxFhzpg==", "path": "devexpress.printing.core/24.1.7", "hashPath": "devexpress.printing.core.24.1.7.nupkg.sha512"}, "DevExpress.RichEdit.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-KmJnUcECp/5uDQ8FLEnhcQhP22/9doSRNlYj+8VnwUh14EsSQ91cNjMmlM7MeDNIgs7a0AvjSETZ9SbvmZltmA==", "path": "devexpress.richedit.core/24.1.7", "hashPath": "devexpress.richedit.core.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Charts/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-+zrEyLyQyL4Z61AzwYQm4sJYlu61T90pNGqAr060a+SJ+kJcsd+rGaVApmIU2qccp2FrM7Zx9+yGDQudWci6OQ==", "path": "devexpress.wpf.charts/24.1.7", "hashPath": "devexpress.wpf.charts.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Controls/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-i8lNbifjrUBXpB2r89wrFEreMBxz6BLQfaI6NyfXVT2xBqIzWawkf4wl0Rp7pil8AmmgfXd65DZbWXKAJBu19A==", "path": "devexpress.wpf.controls/24.1.7", "hashPath": "devexpress.wpf.controls.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-YcMvsXsE8bxtcAMNAF1YATPMQwNDopD8Wlva7CM5ENzAMsMJ3CZA/smrJR3kmSix6Qz8MQAhYpmaqA882/XiZA==", "path": "devexpress.wpf.core/24.1.7", "hashPath": "devexpress.wpf.core.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Docking/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-G7aUM2E+4lV9NmPwkz+NGbxyp2QMe3hezj738N9K8N92BWKt6r2cBJMsYmqPYi20NV1fW5XNlMi+ZGwKaC8XEA==", "path": "devexpress.wpf.docking/24.1.7", "hashPath": "devexpress.wpf.docking.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.DocumentViewer.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-t/vFx04YlFnQRaUPziYZNvLdW8ZdaWR4+e+t0LTuYQGs1ftGUOp0xCAzZ9zQ708A+GqMw9xuXenV9w/jqSPH9A==", "path": "devexpress.wpf.documentviewer.core/24.1.7", "hashPath": "devexpress.wpf.documentviewer.core.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.ExpressionEditor/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-IJQ29JY7RzGwxsgftm/7Kd6haCxxFEFTBtLvZMS0a/l4cRWBra1w54dwlL7J76kJGxxBDCuqSd5P7i0SsbDd2Q==", "path": "devexpress.wpf.expressioneditor/24.1.7", "hashPath": "devexpress.wpf.expressioneditor.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Grid/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-VyRfdmzJjED/ylJFnjlolAmCrPPUdv7ydRbUN/69fW9BI1Ku5ITrxuhKXDmB6I+e1Y6h4bUJQIywpUaU8csb/A==", "path": "devexpress.wpf.grid/24.1.7", "hashPath": "devexpress.wpf.grid.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Grid.Core/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-xZ1U30vsZzSSrbQgXz7Y70xWtyLyLfauE3DsPkcEL0+CgDYY9nevg3ydM9HRkZICLinjyuS0uOWuo2gfTmGAsA==", "path": "devexpress.wpf.grid.core/24.1.7", "hashPath": "devexpress.wpf.grid.core.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Grid.Printing/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-LseU5B5t3j/9/Fsw78JLEjVwGCDFbZJDraWq/2/O5vNUjsijb7FdMMETnuBvIqoe++9LnMUdb513l7jJ9TtT6w==", "path": "devexpress.wpf.grid.printing/24.1.7", "hashPath": "devexpress.wpf.grid.printing.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.LayoutControl/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-gj8T37/RkmmRYDih15rXghUidkfy+Wey7AjEpTAkJ8ZrKKK5rSryh5nsJfbd1dat1Ggzi0DCPPY6GckBN/vPfQ==", "path": "devexpress.wpf.layoutcontrol/24.1.7", "hashPath": "devexpress.wpf.layoutcontrol.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Office/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-eEHSNl20laL1CaFRkMteTZjalDHkcKyaRYMgU2hH6xnLniyKxVx1jU3bfxccBl99+oXQlfTtKeoxNeUag7hYMw==", "path": "devexpress.wpf.office/24.1.7", "hashPath": "devexpress.wpf.office.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Printing/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-DNgmRj9neiSc8iLIJkgFlyL3m7xYY5aDrIrFnmuqDYYEMfnsHFTBRQVFzdVJKwdLbddfJWtEtqUIGTdlsWAGbg==", "path": "devexpress.wpf.printing/24.1.7", "hashPath": "devexpress.wpf.printing.24.1.7.nupkg.sha512"}, "DevExpress.WPF.ProjectTemplates/25.1.1-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-dUDwW9QKw9dxO8aDiGUcn88EJ3SHInb3EiAVYOUtPxoUUK3drQIMosgqT+BThL0NB/P2up6XU2H9Rajx8UmEUQ==", "path": "devexpress.wpf.projecttemplates/25.1.1-alpha", "hashPath": "devexpress.wpf.projecttemplates.25.1.1-alpha.nupkg.sha512"}, "DevExpress.Wpf.PropertyGrid/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-ujcgnDJbtFr7/9zag2OaC5Y6O7IMxAUyuKfcgvt/89emeQ6Yia7uC60b2Iw/aDi3XadCREXNYzr4pFlPx4Y9oA==", "path": "devexpress.wpf.propertygrid/24.1.7", "hashPath": "devexpress.wpf.propertygrid.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Ribbon/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-pgQVw9uI9N1u3hYUfG8XhIL8zhIwmmELOHPxzd3IYjIcPTeNQqACQ3smCBAZtR7wmCZarZdp5y1zq7WOHlqX/Q==", "path": "devexpress.wpf.ribbon/24.1.7", "hashPath": "devexpress.wpf.ribbon.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.RichEdit/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-VUHwBXziYkBIY58ukFL+ZWrSokAmVRgpMPyROU+EtLRC8MMDtX+ffI7VJ1I0dK9EX99IEN2ihKRAmbzCL57fgg==", "path": "devexpress.wpf.richedit/24.1.7", "hashPath": "devexpress.wpf.richedit.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.All/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-XXjnNGl6WeuY4jmSvXxJYJyhdVAUjwRGeTlSAfZLVeYIRn/ngS0at9C73C40MJ+hA6jRI7YDUymSup4CA1l9iw==", "path": "devexpress.wpf.themes.all/24.1.7", "hashPath": "devexpress.wpf.themes.all.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.DXStyle/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-oG6bkknJ7FT3bfzcuNwkSC9zVxJ4MZwY0k0wAbvRKDmnJMKSsOeSfocc20q8xAK4fS4bStpmz/t+8C+iqlsEQw==", "path": "devexpress.wpf.themes.dxstyle/24.1.7", "hashPath": "devexpress.wpf.themes.dxstyle.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.LightGray/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9JttVnEcUyDMWP83sgvoqlJ2S4q3tyZVCecK3NXGvydwk2SbEQkUF7mvDSFGZRGYsH9UmWAg5ZjnMv12wkt9/g==", "path": "devexpress.wpf.themes.lightgray/24.1.7", "hashPath": "devexpress.wpf.themes.lightgray.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.MetropolisDark/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-IWXJDfRA/ctJ0aKZCpAlRcXsD+7y57vE4hpyUu5Oj1O7eeqH+F7vkHqkubBpKs6i+1ZgwN1JV7wo90Gkfu0omA==", "path": "devexpress.wpf.themes.metropolisdark/24.1.7", "hashPath": "devexpress.wpf.themes.metropolisdark.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.MetropolisLight/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-Ulew1QZf9roYxfe09xuZuSUFEB7RdhYvwzfP9kq/QtEHx+4NgstfeAr9dE8WssXOeq67Vt7dhoOYT6UwcAVERQ==", "path": "devexpress.wpf.themes.metropolislight/24.1.7", "hashPath": "devexpress.wpf.themes.metropolislight.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2007Black/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-Sy7FxMRwh+K0jjgeymYkgXBvZW0RiputLqUcptZQWlOodyB55V95vrrOquVtxv0hXv6KJIgImd3I8Todtm/K7A==", "path": "devexpress.wpf.themes.office2007black/24.1.7", "hashPath": "devexpress.wpf.themes.office2007black.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2007Blue/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-uMHwXbNqy8TPoprO+LvYXjsOwVruWpBnnvPcFijEpMg6dT1a/8RpbtmMyLPM23ncH33ff/XBfhd/ZqvKl5NaMg==", "path": "devexpress.wpf.themes.office2007blue/24.1.7", "hashPath": "devexpress.wpf.themes.office2007blue.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2007Silver/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-7GPmnuB2EQ/xIOlEhO+CrQQJGzOucxNwTZ7uFTiCgLYubfA7cELj+ln/29HkLFbqhs7oq324H3LKXTBEyEDGEw==", "path": "devexpress.wpf.themes.office2007silver/24.1.7", "hashPath": "devexpress.wpf.themes.office2007silver.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2010Black/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-VnEEwYwzAyARHYopCrDegssl95ZutUso3kj0rUc52TvZBkw7TqrM70TNxTkg/Um7+zWTJVDbFzpm4qqgGuNWsg==", "path": "devexpress.wpf.themes.office2010black/24.1.7", "hashPath": "devexpress.wpf.themes.office2010black.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2010Blue/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-GTyFAznJE04ytUfyO6dQvgdmw3JRE0U2Oqa/mR68SOPZmBK62Qt0cHKo7tjpn11Xgcnuj3cXVz4+W4toq1aqhQ==", "path": "devexpress.wpf.themes.office2010blue/24.1.7", "hashPath": "devexpress.wpf.themes.office2010blue.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2010Silver/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-gxWYgjz7qnLSVpISHfvfMZRStukZf+pRduRwv36+a7itwQnXolvaMhGRWSctBIlNirdGpshxGV+q3epkcpdUpQ==", "path": "devexpress.wpf.themes.office2010silver/24.1.7", "hashPath": "devexpress.wpf.themes.office2010silver.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2013/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-dNIpc8Z/Agd+HlRrfhqFKknV90g9PWPXqoQ5m0xLHU7MCIa6D0Z8D5la/U5l9m2kYe3Ev1JTNN2uTbMQMu7o/w==", "path": "devexpress.wpf.themes.office2013/24.1.7", "hashPath": "devexpress.wpf.themes.office2013.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2013DarkGray/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-lX4l7g4csH/Evr5Kx4OzDA9+jow60hw8zpQYcqhBm27i15njv1p4GFAFA6xOOBrC4PeeVO+szigKQvTCgPs8tw==", "path": "devexpress.wpf.themes.office2013darkgray/24.1.7", "hashPath": "devexpress.wpf.themes.office2013darkgray.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2013LightGray/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-OZhIhfgSZrVIHrEuj/pwxQpCGG3w8r+5vS7r/XoCW5R1WQQ/2CSj3ZjM1MraD3/MiVHzTxv8EsriumIYE0i3Ew==", "path": "devexpress.wpf.themes.office2013lightgray/24.1.7", "hashPath": "devexpress.wpf.themes.office2013lightgray.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016Black/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-OqsuxcDR+qMxpALpZC7+Mz4DHKpkfy6DqfS9bSX1tP4SCjOcfVmMI5wI8gdV2+rG1aR43e3ky/m4SZWvRR6G0w==", "path": "devexpress.wpf.themes.office2016black/24.1.7", "hashPath": "devexpress.wpf.themes.office2016black.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016BlackSE/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-CkQqiAFAYaP6+TOebBdLcfFK1ByQQg2qTMeLebyvev7N3/J7TeQ7OMWhwrlkm7h1R7coCRISN/E9q78Qyykdfg==", "path": "devexpress.wpf.themes.office2016blackse/24.1.7", "hashPath": "devexpress.wpf.themes.office2016blackse.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016Colorful/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9kTZAc/NzlRItCoqBQWehbktZ7V8KUq71MC+DAKqpnS2sQ9Mc+0R4S2n91Wex9sTLCkvUE5p69Eozc0qkOwM0Q==", "path": "devexpress.wpf.themes.office2016colorful/24.1.7", "hashPath": "devexpress.wpf.themes.office2016colorful.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016ColorfulSE/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-3YOSwAdQh8lQmHnpm4ZokUOCiMwwVzwyq0/qKsTWvlVdtlRnn8kKDeihkPLBk+unbKgO3qnXG3hnINY1BuSf7Q==", "path": "devexpress.wpf.themes.office2016colorfulse/24.1.7", "hashPath": "devexpress.wpf.themes.office2016colorfulse.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016DarkGraySE/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-DhKeW/k2OsgikukiHCpFZMmNUXoLws4rK7ROfijGBCrOIMZoU9jI+lBfkywiPHohAL93/IMwpmfPT7rdcCTydA==", "path": "devexpress.wpf.themes.office2016darkgrayse/24.1.7", "hashPath": "devexpress.wpf.themes.office2016darkgrayse.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016White/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-2beLkUR7TM7vb7+PoZqpEfnJucwhdBnjWKZxRciQPnu6s/WAj7VpZv723sO3lr2XMDqThZBsHUbJCmqsk3aWqA==", "path": "devexpress.wpf.themes.office2016white/24.1.7", "hashPath": "devexpress.wpf.themes.office2016white.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2016WhiteSE/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-N2aJpmCJKj/4Ako194BUigbGJsQRbUYTrfKOMtm1on7DB5lSIUrft0rWa5yKzl5JDI5pqGZOJZbsBPEvCqwEqw==", "path": "devexpress.wpf.themes.office2016whitese/24.1.7", "hashPath": "devexpress.wpf.themes.office2016whitese.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2019Black/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-LvANG4VK0f5WsnzRU4Au4pPWq1K+F0+P7dxlq4NvVIcRSrinqV2FzD5kyN9gv9CJVGALq2asKEuu7AQPkb4Lww==", "path": "devexpress.wpf.themes.office2019black/24.1.7", "hashPath": "devexpress.wpf.themes.office2019black.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2019Colorful/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-vkjtT4sSdDiPSgVfysiZQ0UTthQ8t8/9fZRLzX7pexd487hSYXtE8MGlUezJFRV7eL6aDHHMZRIQG28JbGcjyg==", "path": "devexpress.wpf.themes.office2019colorful/24.1.7", "hashPath": "devexpress.wpf.themes.office2019colorful.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2019DarkGray/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-hJI<PERSON>+itOO1g6yDuq8pfbQ+SRsRM/eO0nuyu6fSqBgNgYTqyKTV4MksXYPwBFAWKQWtbRxp5MZSGh/ihS4kL/BA==", "path": "devexpress.wpf.themes.office2019darkgray/24.1.7", "hashPath": "devexpress.wpf.themes.office2019darkgray.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2019HighContrast/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-gaxzGBHw2ucUOruN+WOV6kNJDHW2lncn8UbuG+KFLJ/VHoNPTO51lkUbN1wpGmipV6OyhyCreqnfDSejUI92Qg==", "path": "devexpress.wpf.themes.office2019highcontrast/24.1.7", "hashPath": "devexpress.wpf.themes.office2019highcontrast.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Office2019White/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-2LquBlmMAQJVPIWgEXnbhSeYpcAYCBXKFbHvLoFSc7+TKeUzBd5eKc5OUDvlQl4PWGapiDh7h1JfVN3lHNnKUQ==", "path": "devexpress.wpf.themes.office2019white/24.1.7", "hashPath": "devexpress.wpf.themes.office2019white.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Seven/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-iiRQ6HGNP4OxQ3xk/DXBICgIM1G4oYOLJvVwYrXhBU7ePzVrFsIC8qbePqW9SRuPZCX862ghP3QKEjVhDxqcxA==", "path": "devexpress.wpf.themes.seven/24.1.7", "hashPath": "devexpress.wpf.themes.seven.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.TouchlineDark/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-hHKEqngVrxvAMbe7bbaf4iNs/zEXKLG+NsYxmDM5oc8av5NytdX5a+Fjtx2VdfoZzBjS5aWSRpJx/MIjAQir5g==", "path": "devexpress.wpf.themes.touchlinedark/24.1.7", "hashPath": "devexpress.wpf.themes.touchlinedark.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2010/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-p6pK5udoYEoM1KsGz7gNjdYUlRgJ0EWaln4WPWrvhAYbRE/u3DALxJqjcVaqj+sjhjC5zC+csuFeV5f1251AHQ==", "path": "devexpress.wpf.themes.vs2010/24.1.7", "hashPath": "devexpress.wpf.themes.vs2010.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2017Blue/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-ghVENsMjvemrm43dHCoE43CwzsRJe7LA3xabmVOhNiMqVYZoT7MiPF2d4/uw46hRIBsITlPQgLAvpZLDAMifQw==", "path": "devexpress.wpf.themes.vs2017blue/24.1.7", "hashPath": "devexpress.wpf.themes.vs2017blue.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2017Dark/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-JlU9fvzsgN19GyXFKuEGChnP65t9//aqGcxl8HfJqOoNUTYofXcU1xyrx/PFGrvZ8yTRoR44Y3UurwDvzRrD7g==", "path": "devexpress.wpf.themes.vs2017dark/24.1.7", "hashPath": "devexpress.wpf.themes.vs2017dark.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2017Light/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-Xl2RFyxXIg0wkeQGu69OYVHFW/37oQiqvPf1F3OsCSyG71OTfInDZjZ3zvkUwvsNiIafGBBw/53y+Q/yaOXJlQ==", "path": "devexpress.wpf.themes.vs2017light/24.1.7", "hashPath": "devexpress.wpf.themes.vs2017light.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2019Blue/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-C++M0+g2Z32o7zD8vlinJBU/qb+mHfkOVZBJigEPY34YxA/oiBXtmos51eMUxM9+w0fuMlR45CIypLe7xhQXJA==", "path": "devexpress.wpf.themes.vs2019blue/24.1.7", "hashPath": "devexpress.wpf.themes.vs2019blue.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2019Dark/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-jy9sLrYGOyfGfB5cpY2Q4QvjWreTZYNDAX7XUDp8F5OHhi44H8NGV8RyAHA+jSeVhDm3loTJ43FvoI8dqSeJ+g==", "path": "devexpress.wpf.themes.vs2019dark/24.1.7", "hashPath": "devexpress.wpf.themes.vs2019dark.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.VS2019Light/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-/ljhGKrpNBeCgNLT4XSGtwe+Wax39ve4AQlquxprS8EIPdK8Cgx28SQQ6ezT11GtoIIsUIsciBCjmDkZiEK5vg==", "path": "devexpress.wpf.themes.vs2019light/24.1.7", "hashPath": "devexpress.wpf.themes.vs2019light.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Win10Dark/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-mbEXNfqB5PLTdMFDRnLwFi0c79OQfM3IFELpsbMxZROxxsyjMjL36laf2saxiHxZSgQI0qNIVSYjaSaMDT8WMQ==", "path": "devexpress.wpf.themes.win10dark/24.1.7", "hashPath": "devexpress.wpf.themes.win10dark.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Win10Light/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-pzsotHzSkwgeroi/HiuDxbRTgk7nGxjTm+wVkE7maJ07ZAxhOUL5KOPrIo1FlWqB4yF5STKXUpV9YiH/QsesRA==", "path": "devexpress.wpf.themes.win10light/24.1.7", "hashPath": "devexpress.wpf.themes.win10light.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Win11Dark/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-VzkoyttrA9d1ZR4Zbrt+cSYnLA6TkYOfVr0JbxmnERt3odifsBAkRG0d+ukV8R4MQAg2xkYijEJggzJShAv1TQ==", "path": "devexpress.wpf.themes.win11dark/24.1.7", "hashPath": "devexpress.wpf.themes.win11dark.24.1.7.nupkg.sha512"}, "DevExpress.Wpf.Themes.Win11Light/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-KZI87cBjkdCohWM4wvI3HL7AHomUw0UA0akrKnNhWDg0VNrXsYaoitcZkxQ6F2JFQc3IN9um7UNaqmd13OrnIQ==", "path": "devexpress.wpf.themes.win11light/24.1.7", "hashPath": "devexpress.wpf.themes.win11light.24.1.7.nupkg.sha512"}, "DevExpress.Xpo/24.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-SxcpuT4jZ+jazhmOy/phbdwd8I70KYN69+M3xmiQPggRuioIehfiEG1oAefLt1v5QtiCxomFirAt+xF4O4Fz6A==", "path": "devexpress.xpo/24.1.7", "hashPath": "devexpress.xpo.24.1.7.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "itext/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-UL1BBg5x3COlcZTPOy/+v0rpbYFtvq7H91fs66rRQ4iMJcQx8TiEo6LCpuwvLTTGFkJZ1FWcWr2aekUkQt6zhg==", "path": "itext/9.2.0", "hashPath": "itext.9.2.0.nupkg.sha512"}, "itext.commons/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-TWZ3q5Dcfsmpl9UN9XEDWL4W7yTxWpx43LeEUHzJUOMEuN9mV9JV5J5aek5sbahCB8Lg9UPULcKpqz499gZxWA==", "path": "itext.commons/9.2.0", "hashPath": "itext.commons.9.2.0.nupkg.sha512"}, "itext7/9.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fsOz8BpXlMN5GJM+7gdpUCqYhOR4GWKT3krnhB6Tbwio7QxIMyos3x7DTfdYsDAPOojr+NW/iKmnDCB4Vc9xWg==", "path": "itext7/9.2.0", "hashPath": "itext7.9.2.0.nupkg.sha512"}, "iTextSharp.LGPLv2.Core/3.7.4": {"type": "package", "serviceable": true, "sha512": "sha512-uMkrgvanNij/sC854OxsD5BZJL8sq3OihBs58pHblmlPHBZ3nVKI2HZRIQcarwUkc4GokaNOArZv7toQJV8HAQ==", "path": "itextsharp.lgplv2.core/3.7.4", "hashPath": "itextsharp.lgplv2.core.3.7.4.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cP5eBSqra4Ae80X72g0h2N+jdrA4BgoMQmz9JaQmKAEXUHw9N21DPIBqIyMjOo2fK9ISiGytlAOxBAJf1hEvqg==", "path": "microsoft.data.sqlite.core/9.0.5", "hashPath": "microsoft.data.sqlite.core.9.0.5.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bl6KYfbFSIW3QIRHAp931iR5h01qHjKghdpAtncwbzNUs0+IUZ+XfwkIU0sQsR33ufGvi3u4dZMIYYFysjpHAA==", "path": "microsoft.dotnet.platformabstractions/1.1.0", "hashPath": "microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-TeCtb/vc+jxvgkVAqeJlZKOoG5w/w8AigWQQyOmeJsJ7+0SkONX8bqEV/wB+ojnT0sXuJrrfXQOEC3ws6asEng==", "path": "microsoft.entityframeworkcore/9.0.5", "hashPath": "microsoft.entityframeworkcore.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-81fGyIibhGc4rq4ZxmVZE/1CFSvGMQOZqdRyCBLKz/Hb8eE973dmSfcdXpXhQ/5f+nbax4VGkWhwPGxWUNWaCQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.5", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-kWRrD69qCXo7lahPZPt7C127UfK0I024laFZEDMfT3JbALB1EWneFvq1utWM0cNKPFuYis1E1oaYTuRGI/9inQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.5", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-xOVWCGRF8DpOIoZ196/g7bdghc2e7Fp6R2vZPKndWv8A64bSDSaS7F2CUoqZpmSphUeT+1HDRpNYFRBQd8H71g==", "path": "microsoft.entityframeworkcore.design/9.0.5", "hashPath": "microsoft.entityframeworkcore.design.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6eErbrZFd9yNnncemtDdmHZ3KC792OQCIYITuMsjK2oh4CLzlYo8mzNsozgUzQ+utHnne11/3eV8zMWbYF5Puw==", "path": "microsoft.entityframeworkcore.relational/9.0.5", "hashPath": "microsoft.entityframeworkcore.relational.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-dy9e3TUiU9COlFTyut6e12bZALM25PDskd6Kk10gbS3rAPYsuaKTkgq3mHDIQOR2bb3WEX7cdNpNF1+r2BIBMg==", "path": "microsoft.entityframeworkcore.sqlite/9.0.5", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-YU11QeQz53xKvr9hV9XABU9nwMMPOJFYuLB5bWgPMoE73ibiprksFzpnWhifRQu0c35jwVTj7kxHIAi/800CXA==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.5", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "path": "microsoft.extensions.caching.abstractions/9.0.5", "hashPath": "microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "path": "microsoft.extensions.caching.memory/9.0.5", "hashPath": "microsoft.extensions.caching.memory.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-+jdJ9Vz+5Ia21l3KjahtmeHCIgQ7urfkdcJPxSfeqB40Jqryi27Lt4fKBmKyvc0YrfTUJ0cEB7QmoQRlU8FH0g==", "path": "microsoft.extensions.dependencymodel/9.0.5", "hashPath": "microsoft.extensions.dependencymodel.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "path": "microsoft.extensions.objectpool/6.0.16", "hashPath": "microsoft.extensions.objectpool.6.0.16.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2210.55": {"type": "package", "serviceable": true, "sha512": "sha512-pb5kuzl8TQm+ztzOnOm/qX75DXXjOQMdKlcs2hyizbZobiGzJcMcIhpmMmgg1cTGYwof2fVhlC809YScvDbm3w==", "path": "microsoft.web.webview2/1.0.2210.55", "hashPath": "microsoft.web.webview2.1.0.2210.55.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SkiaSharp/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-gR9yVoOta2Mc1Rxt15LD65AckfHMfwjIs/3kkD59C9bT2nYYISsE6uz3t4aMPNHA6CgsIL0Ssn+jE5OVilZ1yw==", "path": "skiasharp/3.119.0", "hashPath": "skiasharp.3.119.0.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-YE1vNn0Nyw2PWtv7hw1PYkKJO0itFiQp9vSqGppZUKzQJqwp28a2jgdCMPfYtOiR8KCnDgZqQoynqJRRaE2ZVg==", "path": "skiasharp.nativeassets.macos/3.119.0", "hashPath": "skiasharp.nativeassets.macos.3.119.0.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/3.119.0": {"type": "package", "serviceable": true, "sha512": "sha512-IwC9yx36lOdXVT2DjgmWHl1qkVspfj8ctd4+li8CNnvqdfaTolXCOh6TLznURcPAvzatx9K/tLOB7zT6T8EA9w==", "path": "skiasharp.nativeassets.win32/3.119.0", "hashPath": "skiasharp.nativeassets.win32.3.119.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "path": "system.diagnostics.diagnosticsource/9.0.5", "hashPath": "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JkbHJjtI/dWc5dfmEdJlbe3VwgZqCkZRtfuWFh5GOv0f+gGCfBtzMpIVkmdkj2AObO9y+oiOi81UGwH3aBYuqA==", "path": "system.drawing.common/8.0.0", "hashPath": "system.drawing.common.8.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-glgtKqWJpH9GDw0m9I5xFiF6WDIQqi/eZXU6MkMRPzAWEERGGAJh+qztkrlWSDbokQ1jalj5NcBNIvVoSDpSSA==", "path": "system.formats.asn1/6.0.1", "hashPath": "system.formats.asn1.6.0.1.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "path": "system.io.pipelines/9.0.5", "hashPath": "system.io.pipelines.9.0.5.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-18UT1BdZ4TYFBHk/wuq7JzCdE3X75z81X+C2rXqIlmEnC21Pm60spGV/dKQSzbyomstqYE33EuN5hfnC86VFxA==", "path": "system.security.cryptography.pkcs/6.0.3", "hashPath": "system.security.cryptography.pkcs.6.0.3.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.ServiceModel.Http/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lMk8MEw1OCvyyKY4HMg4ro1eYtWY7azIoDc2FBEGP8uOTJouWn3DemOQvM/GUpgrFbkpjuHPbEG5hgUbNtpiYA==", "path": "system.servicemodel.http/6.2.0", "hashPath": "system.servicemodel.http.6.2.0.nupkg.sha512"}, "System.ServiceModel.NetFramingBase/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-204c9SNDKyQrDKv6F9MLlWKnM7UthRErFByJCHj8y9DtcgMAQnEB5xJvh+9ECmJgG13LJLOAMB5f3CjMatzz/A==", "path": "system.servicemodel.netframingbase/6.2.0", "hashPath": "system.servicemodel.netframingbase.6.2.0.nupkg.sha512"}, "System.ServiceModel.NetTcp/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXTDhh8DgCfNyY5k9sNlqvhBVYqVM+0GZBsJfFMH5P5q7qGmTxql3bG9tae1Z+uMXJpG2jLbo1CfgusZ75lADA==", "path": "system.servicemodel.nettcp/6.2.0", "hashPath": "system.servicemodel.nettcp.6.2.0.nupkg.sha512"}, "System.ServiceModel.Primitives/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ro+c4JKNuX6dDpTWh9ZICYr4pIe7uJToauPPgZt2qqFPjVB78ZDUz3rPCZX89dA+IoRZ+9T1ngLBKsgkTmx7UA==", "path": "system.servicemodel.primitives/6.2.0", "hashPath": "system.servicemodel.primitives.6.2.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IRiEFUa5b/Gs5Egg8oqBVoywhtOeaO2KOx3j0RfcYY/raxqBuEK7NXRDgOwtYM8qbi+7S4RPXUbNt+ZxyY0/NQ==", "path": "system.text.encoding.codepages/4.3.0", "hashPath": "system.text.encoding.codepages.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-HJPmqP2FsE+WVUUlTsZ4IFRSyzw40yz0ubiTnsaqm+Xo5fFZhVRvx6Zn8tLXj92/6pbre6OA4QL2A2vnCSKxJA==", "path": "system.text.encodings.web/9.0.5", "hashPath": "system.text.encodings.web.9.0.5.nupkg.sha512"}, "System.Text.Json/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "path": "system.text.json/9.0.5", "hashPath": "system.text.json.9.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}}}