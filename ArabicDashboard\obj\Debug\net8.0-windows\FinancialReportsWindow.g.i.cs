﻿#pragma checksum "..\..\..\FinancialReportsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "24348166230F8E6AED5C07BD40220C89F90BFDDD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// FinancialReportsWindow
    /// </summary>
    public partial class FinancialReportsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 64 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateFrom;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateTo;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbReportType;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnGenerateReport;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnExportReport;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnRefreshData;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnExportExcel;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalRevenue;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalExpenses;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNetProfit;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTransactionCount;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAverageTransaction;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReportTitle;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReportInfo;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridReport;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLastUpdate;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\FinancialReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatus;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/financialreportswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\FinancialReportsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DateFrom = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 2:
            this.DateTo = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 3:
            this.CmbReportType = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            
            #line 80 "..\..\..\FinancialReportsWindow.xaml"
            this.CmbReportType.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.CmbReportType_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnGenerateReport = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 88 "..\..\..\FinancialReportsWindow.xaml"
            this.BtnGenerateReport.Click += new System.Windows.RoutedEventHandler(this.BtnGenerateReport_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnExportReport = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 101 "..\..\..\FinancialReportsWindow.xaml"
            this.BtnExportReport.Click += new System.Windows.RoutedEventHandler(this.BtnExportReport_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnRefreshData = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 114 "..\..\..\FinancialReportsWindow.xaml"
            this.BtnRefreshData.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnExportExcel = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 127 "..\..\..\FinancialReportsWindow.xaml"
            this.BtnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtTotalRevenue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtTotalExpenses = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtNetProfit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtTransactionCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtAverageTransaction = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtReportTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtReportInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.GridReport = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 16:
            this.TxtLastUpdate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TxtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

