﻿#pragma checksum "..\..\..\InquiryCalculationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6F487A9D70F19209674E0FF827847370CABEBF88"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// InquiryCalculationWindow
    /// </summary>
    public partial class InquiryCalculationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 84 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtWorkerName;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtResidenceNumber;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbServiceType;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbWorkerType;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinPassportFees;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridWorkPermitFees;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinWorkPermitFees;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridSaudiEmployment;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinSaudiEmployment;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridLaborOfficeFees;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinLaborOfficeFees;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridServiceFees;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinServiceFees;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridTransferFees;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinTransferFees;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StackSponsorOption;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.CheckEdit ChkHasSponsor;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridSponsorFees;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinSponsorFees;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StackWorkPermitDate;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateWorkPermitIssue;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtWorkPermitIssueHijri;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StackRenewalEligibilityStatus;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BorderRenewalEligibilityStatus;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRenewalEligibilityIcon;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRenewalEligibilityStatus;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StackWorkPermitEligibilityStatus;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BorderWorkPermitEligibilityStatus;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtWorkPermitEligibilityIcon;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtWorkPermitEligibilityStatus;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StackManualRenewal;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.CheckEdit ChkManualRenewal;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridManualRenewalFees;
        
        #line default
        #line hidden
        
        
        #line 457 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.SpinEdit SpinManualRenewalFees;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnCalculate;
        
        #line default
        #line hidden
        
        
        #line 476 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnReset;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnSave;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtResidenceInfo;
        
        #line default
        #line hidden
        
        
        #line 532 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StackCalculationDetails;
        
        #line default
        #line hidden
        
        
        #line 546 "..\..\..\InquiryCalculationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/inquirycalculationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\InquiryCalculationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtWorkerName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 2:
            this.TxtResidenceNumber = ((DevExpress.Xpf.Editors.TextEdit)(target));
            
            #line 99 "..\..\..\InquiryCalculationWindow.xaml"
            this.TxtResidenceNumber.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.TxtResidenceNumber_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CmbServiceType = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            
            #line 137 "..\..\..\InquiryCalculationWindow.xaml"
            this.CmbServiceType.SelectedIndexChanged += new System.Windows.RoutedEventHandler(this.CmbServiceType_SelectedIndexChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CmbWorkerType = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            
            #line 153 "..\..\..\InquiryCalculationWindow.xaml"
            this.CmbWorkerType.SelectedIndexChanged += new System.Windows.RoutedEventHandler(this.CmbWorkerType_SelectedIndexChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SpinPassportFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 6:
            this.GridWorkPermitFees = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.SpinWorkPermitFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 8:
            this.GridMedicalInsurance = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.SpinMedicalInsurance = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 10:
            this.GridSaudiEmployment = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.SpinSaudiEmployment = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 12:
            this.GridLaborOfficeFees = ((System.Windows.Controls.Grid)(target));
            return;
            case 13:
            this.SpinLaborOfficeFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 14:
            this.GridServiceFees = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.SpinServiceFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 16:
            this.GridTransferFees = ((System.Windows.Controls.Grid)(target));
            return;
            case 17:
            this.SpinTransferFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 18:
            this.StackSponsorOption = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.ChkHasSponsor = ((DevExpress.Xpf.Editors.CheckEdit)(target));
            
            #line 259 "..\..\..\InquiryCalculationWindow.xaml"
            this.ChkHasSponsor.Checked += new System.Windows.RoutedEventHandler(this.ChkHasSponsor_Checked);
            
            #line default
            #line hidden
            
            #line 260 "..\..\..\InquiryCalculationWindow.xaml"
            this.ChkHasSponsor.Unchecked += new System.Windows.RoutedEventHandler(this.ChkHasSponsor_Unchecked);
            
            #line default
            #line hidden
            return;
            case 20:
            this.GridSponsorFees = ((System.Windows.Controls.Grid)(target));
            return;
            case 21:
            this.SpinSponsorFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 22:
            this.StackWorkPermitDate = ((System.Windows.Controls.Border)(target));
            return;
            case 23:
            this.DateWorkPermitIssue = ((DevExpress.Xpf.Editors.DateEdit)(target));
            
            #line 316 "..\..\..\InquiryCalculationWindow.xaml"
            this.DateWorkPermitIssue.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.DateWorkPermitIssue_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.TxtWorkPermitIssueHijri = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 25:
            this.StackRenewalEligibilityStatus = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.BorderRenewalEligibilityStatus = ((System.Windows.Controls.Border)(target));
            return;
            case 27:
            this.TxtRenewalEligibilityIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.TxtRenewalEligibilityStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.StackWorkPermitEligibilityStatus = ((System.Windows.Controls.Border)(target));
            return;
            case 30:
            this.BorderWorkPermitEligibilityStatus = ((System.Windows.Controls.Border)(target));
            return;
            case 31:
            this.TxtWorkPermitEligibilityIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.TxtWorkPermitEligibilityStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.StackManualRenewal = ((System.Windows.Controls.Border)(target));
            return;
            case 34:
            this.ChkManualRenewal = ((DevExpress.Xpf.Editors.CheckEdit)(target));
            
            #line 447 "..\..\..\InquiryCalculationWindow.xaml"
            this.ChkManualRenewal.Checked += new System.Windows.RoutedEventHandler(this.ChkManualRenewal_Checked);
            
            #line default
            #line hidden
            
            #line 448 "..\..\..\InquiryCalculationWindow.xaml"
            this.ChkManualRenewal.Unchecked += new System.Windows.RoutedEventHandler(this.ChkManualRenewal_Unchecked);
            
            #line default
            #line hidden
            return;
            case 35:
            this.GridManualRenewalFees = ((System.Windows.Controls.Grid)(target));
            return;
            case 36:
            this.SpinManualRenewalFees = ((DevExpress.Xpf.Editors.SpinEdit)(target));
            return;
            case 37:
            this.BtnCalculate = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 467 "..\..\..\InquiryCalculationWindow.xaml"
            this.BtnCalculate.Click += new System.Windows.RoutedEventHandler(this.BtnCalculate_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.BtnReset = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 477 "..\..\..\InquiryCalculationWindow.xaml"
            this.BtnReset.Click += new System.Windows.RoutedEventHandler(this.BtnReset_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.BtnSave = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 487 "..\..\..\InquiryCalculationWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.TxtResidenceInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.StackCalculationDetails = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 42:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

