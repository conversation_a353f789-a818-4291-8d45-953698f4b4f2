﻿#pragma checksum "..\..\..\InvoicePreviewWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A74B989AF9D775D3DD8987EB4CF4389920D46A12"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using DevExpress.Xpf.LayoutControl;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// InvoicePreviewWindow
    /// </summary>
    public partial class InvoicePreviewWindow : DevExpress.Xpf.Core.ThemedWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 52 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ToolbarGrid;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrint;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportPdf;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSendEmail;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClose;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InvoiceContainer;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusStamp;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatusStamp;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtClientName;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPhoneNumber;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtClientIdNumber;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtClientAddress;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtIssueDate;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtDueDate;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInvoiceType;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtQuantity;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTransactionType;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAmount;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmountRow;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPaidAmount;
        
        #line default
        #line hidden
        
        
        #line 421 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRemainingAmount;
        
        #line default
        #line hidden
        
        
        #line 437 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PaymentProgressBar;
        
        #line default
        #line hidden
        
        
        #line 440 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPaymentPercentage;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotesSection;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\InvoicePreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtElectronicNote;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/invoicepreviewwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\InvoicePreviewWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ToolbarGrid = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.BtnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\InvoicePreviewWindow.xaml"
            this.BtnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnExportPdf = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\InvoicePreviewWindow.xaml"
            this.BtnExportPdf.Click += new System.Windows.RoutedEventHandler(this.BtnExportPdf_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnSendEmail = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\InvoicePreviewWindow.xaml"
            this.BtnSendEmail.Click += new System.Windows.RoutedEventHandler(this.BtnSendEmail_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnClose = ((System.Windows.Controls.Button)(target));
            
            #line 76 "..\..\..\InvoicePreviewWindow.xaml"
            this.BtnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.InvoiceContainer = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.StatusStamp = ((System.Windows.Controls.Border)(target));
            return;
            case 8:
            this.TxtStatusStamp = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtClientName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtPhoneNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtClientIdNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtClientAddress = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtInvoiceNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtIssueDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtDueDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TxtInvoiceType = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TxtQuantity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TxtTransactionType = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.TxtAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.TxtTotalAmountRow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TxtPaidAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.TxtRemainingAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.PaymentProgressBar = ((System.Windows.Controls.Border)(target));
            return;
            case 25:
            this.TxtPaymentPercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.NotesSection = ((System.Windows.Controls.Border)(target));
            return;
            case 27:
            this.TxtNotes = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.TxtElectronicNote = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

