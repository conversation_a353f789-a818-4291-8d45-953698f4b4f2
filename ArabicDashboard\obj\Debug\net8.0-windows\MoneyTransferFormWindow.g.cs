﻿#pragma checksum "..\..\..\MoneyTransferFormWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7316027755BE88EB5FFC0FFCB86B70B37B8E4EDE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// MoneyTransferFormWindow
    /// </summary>
    public partial class MoneyTransferFormWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 24 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTitle;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtTransferNumber;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSenderName;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSenderPhone;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.ComboBoxEdit CmbSenderBank;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtReceiptPath;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnBrowseReceipt;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit NumAmount;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit NumServiceFee;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit NumTax;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.MemoEdit TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnSave;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\MoneyTransferFormWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/moneytransferformwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MoneyTransferFormWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtTransferNumber = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 3:
            this.TxtSenderName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 4:
            this.TxtSenderPhone = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 5:
            this.CmbSenderBank = ((DevExpress.Xpf.Editors.ComboBoxEdit)(target));
            return;
            case 6:
            this.TxtReceiptPath = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 7:
            this.BtnBrowseReceipt = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 102 "..\..\..\MoneyTransferFormWindow.xaml"
            this.BtnBrowseReceipt.Click += new System.Windows.RoutedEventHandler(this.BtnBrowseReceipt_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NumAmount = ((DevExpress.Xpf.Editors.TextEdit)(target));
            
            #line 132 "..\..\..\MoneyTransferFormWindow.xaml"
            this.NumAmount.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.NumAmount_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.NumServiceFee = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 10:
            this.NumTax = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 11:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtNotes = ((DevExpress.Xpf.Editors.MemoEdit)(target));
            return;
            case 13:
            this.BtnSave = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 177 "..\..\..\MoneyTransferFormWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnCancel = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 190 "..\..\..\MoneyTransferFormWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

