﻿#pragma checksum "..\..\..\MoneyTransferPrintWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "54021E5432865CDB90CE987C5859EB7321A1E9BC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// MoneyTransferPrintWindow
    /// </summary>
    public partial class MoneyTransferPrintWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnPrintPDF;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnPrint;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnClose;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer PrintArea;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTransferNumber;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTransferDate;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSenderName;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSenderPhone;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSenderCountry;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSenderCity;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReceiverName;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReceiverPhone;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReceiverCountry;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReceiverCity;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAmount;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtServiceFee;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTax;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatus;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\MoneyTransferPrintWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPrintDate;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/moneytransferprintwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MoneyTransferPrintWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnPrintPDF = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 28 "..\..\..\MoneyTransferPrintWindow.xaml"
            this.BtnPrintPDF.Click += new System.Windows.RoutedEventHandler(this.BtnPrintPDF_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnPrint = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 41 "..\..\..\MoneyTransferPrintWindow.xaml"
            this.BtnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnClose = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 54 "..\..\..\MoneyTransferPrintWindow.xaml"
            this.BtnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PrintArea = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 5:
            this.TxtTransferNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtTransferDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtSenderName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtSenderPhone = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtSenderCountry = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtSenderCity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtReceiverName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtReceiverPhone = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtReceiverCountry = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtReceiverCity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TxtServiceFee = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TxtTax = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 20:
            this.TxtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.TxtNotes = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.TxtPrintDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

