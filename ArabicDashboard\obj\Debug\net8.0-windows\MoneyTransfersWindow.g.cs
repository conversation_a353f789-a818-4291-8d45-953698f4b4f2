﻿#pragma checksum "..\..\..\MoneyTransfersWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "19ADC4645F04F30ECF484EBD0B6DA6215403A24E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// MoneyTransfersWindow
    /// </summary>
    public partial class MoneyTransfersWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnAddTransfer;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnEditTransfer;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnDeleteTransfer;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Core.SimpleButton BtnPrintTransfer;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalTransfers;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCompletedTransfers;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPendingTransfers;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRecordCount;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridTransfers;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLastUpdate;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\MoneyTransfersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStatus;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/moneytransferswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MoneyTransfersWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnAddTransfer = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 58 "..\..\..\MoneyTransfersWindow.xaml"
            this.BtnAddTransfer.Click += new System.Windows.RoutedEventHandler(this.BtnAddTransfer_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnEditTransfer = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 71 "..\..\..\MoneyTransfersWindow.xaml"
            this.BtnEditTransfer.Click += new System.Windows.RoutedEventHandler(this.BtnEditTransfer_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnDeleteTransfer = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 84 "..\..\..\MoneyTransfersWindow.xaml"
            this.BtnDeleteTransfer.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteTransfer_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnPrintTransfer = ((DevExpress.Xpf.Core.SimpleButton)(target));
            
            #line 97 "..\..\..\MoneyTransfersWindow.xaml"
            this.BtnPrintTransfer.Click += new System.Windows.RoutedEventHandler(this.BtnPrintTransfer_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TxtTotalTransfers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtCompletedTransfers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtPendingTransfers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtRecordCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.GridTransfers = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 11:
            this.TxtLastUpdate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

