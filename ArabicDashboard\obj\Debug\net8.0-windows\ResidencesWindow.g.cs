﻿#pragma checksum "..\..\..\ResidencesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5969CB5D8D55695E17269AA6385F33B38FF6288C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using ArabicDashboard;
using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// ResidencesWindow
    /// </summary>
    public partial class ResidencesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 142 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnShowData;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddResidence;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DataSection;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalResidences;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExpiredResidences;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExpiringResidences;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtExpiringMedicalInsurance;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEdit;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDelete;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridResidences;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InputPanel;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FormTitle;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtWorkerName;
        
        #line default
        #line hidden
        
        
        #line 348 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.TextEdit TxtResidenceNumber;
        
        #line default
        #line hidden
        
        
        #line 355 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateRenewal;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateExpiry;
        
        #line default
        #line hidden
        
        
        #line 369 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.DateEdit DateMedicalInsuranceExpiry;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Editors.MemoEdit TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\ResidencesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;component/residenceswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ResidencesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.BtnShowData = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\ResidencesWindow.xaml"
            this.BtnShowData.Click += new System.Windows.RoutedEventHandler(this.BtnShowData_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnAddResidence = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\ResidencesWindow.xaml"
            this.BtnAddResidence.Click += new System.Windows.RoutedEventHandler(this.BtnAddResidence_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DataSection = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.TxtTotalResidences = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TxtExpiredResidences = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtExpiringResidences = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtExpiringMedicalInsurance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtSearch = ((DevExpress.Xpf.Editors.TextEdit)(target));
            
            #line 254 "..\..\..\ResidencesWindow.xaml"
            this.TxtSearch.EditValueChanged += new DevExpress.Xpf.Editors.EditValueChangedEventHandler(this.TxtSearch_EditValueChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnEdit = ((System.Windows.Controls.Button)(target));
            
            #line 260 "..\..\..\ResidencesWindow.xaml"
            this.BtnEdit.Click += new System.Windows.RoutedEventHandler(this.BtnEdit_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnDelete = ((System.Windows.Controls.Button)(target));
            
            #line 262 "..\..\..\ResidencesWindow.xaml"
            this.BtnDelete.Click += new System.Windows.RoutedEventHandler(this.BtnDelete_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 264 "..\..\..\ResidencesWindow.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.GridResidences = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            case 14:
            this.InputPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 15:
            this.FormTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TxtWorkerName = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 17:
            this.TxtResidenceNumber = ((DevExpress.Xpf.Editors.TextEdit)(target));
            return;
            case 18:
            this.DateRenewal = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 19:
            this.DateExpiry = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 20:
            this.DateMedicalInsuranceExpiry = ((DevExpress.Xpf.Editors.DateEdit)(target));
            return;
            case 21:
            this.TxtNotes = ((DevExpress.Xpf.Editors.MemoEdit)(target));
            return;
            case 22:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 385 "..\..\..\ResidencesWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 387 "..\..\..\ResidencesWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

