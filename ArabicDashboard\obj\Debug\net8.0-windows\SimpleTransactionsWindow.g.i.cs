﻿#pragma checksum "..\..\..\SimpleTransactionsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7021E5B12EA0F871CB89E00BA47BD33EFD205B92"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using DevExpress.Xpf.Editors;
using DevExpress.Xpf.Editors.DataPager;
using DevExpress.Xpf.Editors.DateNavigator;
using DevExpress.Xpf.Editors.ExpressionEditor;
using DevExpress.Xpf.Editors.Filtering;
using DevExpress.Xpf.Editors.Flyout;
using DevExpress.Xpf.Editors.Popups;
using DevExpress.Xpf.Editors.Popups.Calendar;
using DevExpress.Xpf.Editors.RangeControl;
using DevExpress.Xpf.Editors.Settings;
using DevExpress.Xpf.Editors.Settings.Extension;
using DevExpress.Xpf.Editors.Validation;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Grid.ConditionalFormatting;
using DevExpress.Xpf.Grid.LookUp;
using DevExpress.Xpf.Grid.TreeList;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArabicDashboard {
    
    
    /// <summary>
    /// SimpleTransactionsWindow
    /// </summary>
    public partial class SimpleTransactionsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddTransaction;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnViewTransactions;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContent;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer InputSection;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtClientName;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServiceType;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtServiceFees;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtGovernmentFees;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLaborOfficeFees;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNetProfit;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatus;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DataSection;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEdit;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDelete;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\SimpleTransactionsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DevExpress.Xpf.Grid.GridControl GridTransactions;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArabicDashboard;V1.0.0.0;component/simpletransactionswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SimpleTransactionsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnAddTransaction = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\SimpleTransactionsWindow.xaml"
            this.BtnAddTransaction.Click += new System.Windows.RoutedEventHandler(this.BtnAddTransaction_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnViewTransactions = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\SimpleTransactionsWindow.xaml"
            this.BtnViewTransactions.Click += new System.Windows.RoutedEventHandler(this.BtnViewTransactions_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MainContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.InputSection = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 5:
            this.TxtClientName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtServiceType = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.TxtServiceFees = ((System.Windows.Controls.TextBox)(target));
            
            #line 96 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtServiceFees.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtGovernmentFees = ((System.Windows.Controls.TextBox)(target));
            
            #line 102 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtGovernmentFees.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TxtLaborOfficeFees = ((System.Windows.Controls.TextBox)(target));
            
            #line 108 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtLaborOfficeFees.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 10:
            this.TxtNetProfit = ((System.Windows.Controls.TextBox)(target));
            
            #line 114 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtNetProfit.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.CmbStatus = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 140 "..\..\..\SimpleTransactionsWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 141 "..\..\..\SimpleTransactionsWindow.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DataSection = ((System.Windows.Controls.Grid)(target));
            return;
            case 17:
            this.BtnEdit = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\SimpleTransactionsWindow.xaml"
            this.BtnEdit.Click += new System.Windows.RoutedEventHandler(this.BtnEdit_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.BtnDelete = ((System.Windows.Controls.Button)(target));
            
            #line 181 "..\..\..\SimpleTransactionsWindow.xaml"
            this.BtnDelete.Click += new System.Windows.RoutedEventHandler(this.BtnDelete_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 197 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtSearch.GotFocus += new System.Windows.RoutedEventHandler(this.TxtSearch_GotFocus);
            
            #line default
            #line hidden
            
            #line 197 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtSearch.LostFocus += new System.Windows.RoutedEventHandler(this.TxtSearch_LostFocus);
            
            #line default
            #line hidden
            
            #line 198 "..\..\..\SimpleTransactionsWindow.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.GridTransactions = ((DevExpress.Xpf.Grid.GridControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

